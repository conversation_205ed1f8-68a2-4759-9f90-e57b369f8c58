{"name": "react-countup", "version": "6.5.3", "description": "A React component wrapper around CountUp.js", "author": "<PERSON> <<EMAIL>> (https://twitter.com/glnnrys)", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "license": "MIT", "repository": "glennreyes/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "homepage": "https://react-countup.now.sh", "main": "build", "files": ["build/index.js", "build/index.d.ts"], "typings": "build/index.d.ts", "scripts": {"format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "build": "rm -rf build && rollup --bundleConfigAsCjs -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "prepack": "yarn build", "test": "jest"}, "peerDependencies": {"react": ">= 16.3.0"}, "dependencies": {"countup.js": "^2.8.0"}, "devDependencies": {"@babel/core": "7.23.9", "@babel/preset-env": "7.24.0", "@babel/preset-react": "7.23.3", "@babel/preset-typescript": "^7.23.2", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-node-resolve": "^15.2.3", "@testing-library/react": "14.2.0", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "babel-jest": "29.7.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "3.2.4", "pretty-quick": "3.1.3", "react": "18.2.0", "react-dom": "18.2.0", "react-test-renderer": "18.2.0", "rollup": "4.9.6", "typescript": "^5.2.2"}}