"use strict";

var assert  = require("chai").assert
  , isError = require("../../error/is");

describe("error/is", function () {
	it("Should return true on error", function () { assert.equal(isError(new Error()), true); });

	it("Should return false on native error with no common API exposed", function () {
		var value = new Error();
		value.message = null;
		assert.equal(isError(value), false);
	});
	it("Should return false on Error.prototype", function () {
		assert.equal(isError(Error.prototype), false);
	});

	if (typeof Object.create === "function") {
		it("Should return true on custom built ES5 era error", function () {
			var CustomEs5Error = function () { Error.call(this); };
			CustomEs5Error.prototype = Object.create(Error.prototype);
			assert.equal(isError(new CustomEs5Error()), true);
		});

		it("Should return false on object with no prototype", function () {
			assert.equal(isError(Object.create(null)), false);
		});
	}

	it("Should return false on plain object", function () { assert.equal(isError({}), false); });
	it("Should return false on function", function () {
		assert.equal(isError(function () { return true; }), false);
	});

	it("Should return false on array", function () { assert.equal(isError([]), false); });

	it("Should return false on string", function () { assert.equal(isError("foo"), false); });
	it("Should return false on empty string", function () { assert.equal(isError(""), false); });
	it("Should return false on number", function () { assert.equal(isError(123), false); });
	it("Should return false on NaN", function () { assert.equal(isError(NaN), false); });
	it("Should return false on boolean", function () { assert.equal(isError(true), false); });
	if (typeof Symbol === "function") {
		it("Should return false on symbol", function () {
			assert.equal(isError(Symbol("foo")), false);
		});
	}

	it("Should return false on null", function () { assert.equal(isError(null), false); });
	it("Should return false on undefined", function () { assert.equal(isError(void 0), false); });
});
