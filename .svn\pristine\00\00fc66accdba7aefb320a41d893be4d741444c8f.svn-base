// -----------------------------------------------------------------------------------------
// -----------------------------------------------------------------------------------------
// This file overrides licenses only for OSS components which do not appear in `cgmanifest.json`.
// i.e. for OSS components that are detected from `package-lock.json` or `Cargo.lock` files.
//
// DO NOT EDIT THIS FILE UNLESS THE OSS TOOL INDICATES THAT YOU SHOULD.
//
[
	{
		// Reason: The license at https://github.com/aadsm/jschardet/blob/master/LICENSE
		// does not include a clear Copyright statement and does not credit authors.
		"name": "jschardet",
		"prependLicenseText": [
			"<PERSON><PERSON><PERSON> was originally ported from C++ by <PERSON>. It is now maintained",
			" by <PERSON><PERSON> and <PERSON>rdasco, and was formerly maintained by <PERSON>.",
			" JSChardet was ported from python to JavaScript by António Afonso ",
			" (https://github.com/aadsm/jschardet) and transformed into an npm package by ",
			"Markus Ast (https://github.com/brainafk)"
		]
	},
	{
		// Reason: The license at https://github.com/microsoft/TypeScript/blob/master/LICENSE.txt
		// does not include a clear Copyright statement.
		"name": "typescript",
		"prependLicenseText": [
			"Copyright (c) Microsoft Corporation. All rights reserved."
		]
	},
	{
		"name": "tunnel-agent",
		"prependLicenseText": [
			"Copyright (c) tunnel-agent authors"
		]
	},
	{
		// Reason: The license at https://github.com/rbuckton/reflect-metadata/blob/master/LICENSE
		// does not include a clear Copyright statement (it's in https://github.com/rbuckton/reflect-metadata/blob/master/CopyrightNotice.txt).
		"name": "reflect-metadata",
		"prependLicenseText": [
			"Copyright (c) Microsoft Corporation. All rights reserved."
		]
	},
	{
		// Reason: The license cannot be found by the tool due to access controls on the repository
		"name": "vscode-tas-client",
		"fullLicenseText": [
			"MIT License",
			"Copyright (c) 2020 - present Microsoft Corporation",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		// Reason: The license cannot be found by the tool due to access controls on the repository
		"name": "tas-client",
		"fullLicenseText": [
			"MIT License",
			"Copyright (c) 2020 - present Microsoft Corporation",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		// Reason: The license cannot be found by the tool due to access controls on the repository
		"name": "tas-client-umd",
		"fullLicenseText": [
			"MIT License",
			"Copyright (c) 2020 - present Microsoft Corporation",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		// Reason: Repository lacks license text.
		// https://github.com/tjwebb/fnv-plus/blob/master/package.json declares MIT.
		// https://github.com/tjwebb/fnv-plus/issues/14
		"name": "@enonic/fnv-plus",
		"fullLicenseText": [
			"MIT License",
			"Copyright (c) 2014 - present, Travis Webb <<EMAIL>>",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "@vscode/win32-app-container-tokens",
		"fullLicenseText": [
			"MIT License",
			"",
			"Copyright (c) Microsoft Corporation.",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy",
			"of this software and associated documentation files (the \"Software\"), to deal",
			"in the Software without restriction, including without limitation the rights",
			"to use, copy, modify, merge, publish, distribute, sublicense, and/or sell",
			"copies of the Software, and to permit persons to whom the Software is",
			"furnished to do so, subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,",
			"FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE",
			"AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER",
			"LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,",
			"OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE",
			"SOFTWARE"
		]
	},
	{
		// Reason: NPM package does not include repository URL https://github.com/microsoft/vscode-deviceid/issues/12
		"name": "@vscode/deviceid",
		"fullLicenseText": [
			"Copyright (c) Microsoft Corporation.",
			"",
			"MIT License",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED *AS IS*, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		// Reason: Missing license file
		"name": "@tokenizer/token",
		"fullLicenseText": [
			"(The MIT License)",
			"",
			"Copyright (c) 2020 Borewit",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the 'Software'), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		// Reason: Missing license file
		"name": "readable-web-to-node-stream",
		"fullLicenseText": [
			"(The MIT License)",
			"",
			"Copyright (c) 2019 Borewit",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the 'Software'), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		// Reason: The substack org has been deleted on GH
		"name": "concat-map",
		"fullLicenseText": [
			"This software is released under the MIT license:",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of",
			"this software and associated documentation files (the \"Software\"), to deal in",
			"the Software without restriction, including without limitation the rights to",
			"use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of",
			"the Software, and to permit persons to whom the Software is furnished to do so,",
			"subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS",
			"FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR",
			"COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER",
			"IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN",
			"CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		// Reason: The substack org has been deleted on GH
		"name": "github-from-package",
		"fullLicenseText": [
			"This software is released under the MIT license:",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of",
			"this software and associated documentation files (the \"Software\"), to deal in",
			"the Software without restriction, including without limitation the rights to",
			"use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of",
			"the Software, and to permit persons to whom the Software is furnished to do so,",
			"subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS",
			"FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR",
			"COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER",
			"IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN",
			"CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		// Reason: The substack org has been deleted on GH
		"name": "minimist",
		"fullLicenseText": [
			"This software is released under the MIT license:",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of",
			"this software and associated documentation files (the \"Software\"), to deal in",
			"the Software without restriction, including without limitation the rights to",
			"use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of",
			"the Software, and to permit persons to whom the Software is furnished to do so,",
			"subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS",
			"FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR",
			"COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER",
			"IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN",
			"CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		// Reason: repo URI is wrong on crate, pending https://github.com/warp-tech/russh/pull/53
		"name": "russh-cryptovec",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/warp-tech/russh/1da80d0d599b6ee2d257c544c0d6af4f649c9029/LICENSE-2.0.txt"
	},
	{
		// Reason: repo URI is wrong on crate, pending https://github.com/warp-tech/russh/pull/53
		"name": "russh-keys",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/warp-tech/russh/1da80d0d599b6ee2d257c544c0d6af4f649c9029/LICENSE-2.0.txt"
	},
	{
		// Reason: license is in a subdirectory in repo
		"name": "dirs-next",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/xdg-rs/dirs/af4aa39daba0ac68e222962a5aca17360158b7cc/dirs/LICENSE-MIT"
	},
	{
		// Reason: license is in a subdirectory in repo
		"name": "openssl",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/sfackler/rust-openssl/e43eb58540b27a17f8029c397e3edc12bbc9011f/openssl/LICENSE"
	},
	{
		// Reason: license is in a subdirectory in repo
		"name": "openssl-sys",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/sfackler/rust-openssl/e43eb58540b27a17f8029c397e3edc12bbc9011f/openssl-sys/LICENSE-MIT"
	},
	{
		// Reason: Missing license file
		"name": "openssl-macros",
		"fullLicenseText": [
			"This software is released under the MIT license:",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of",
			"this software and associated documentation files (the \"Software\"), to deal in",
			"the Software without restriction, including without limitation the rights to",
			"use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of",
			"the Software, and to permit persons to whom the Software is furnished to do so,",
			"subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS",
			"FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR",
			"COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER",
			"IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN",
			"CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{ // Reason: Missing license file
		"name": "const_format_proc_macros",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/rodrimati1992/const_format_crates/b2207af46bfbd9f1a6bd12dbffd10feeea3d9fd7/LICENSE-ZLIB.md"
	},
	{ // Reason: Missing license file
		"name": "const_format",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/rodrimati1992/const_format_crates/b2207af46bfbd9f1a6bd12dbffd10feeea3d9fd7/LICENSE-ZLIB.md"
	},
	{ // License is MIT/Apache and tool doesn't look in subfolders
		"name": "toml_edit",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/toml-rs/toml/main/crates/toml_edit/LICENSE-MIT"
	},
	{ // License is MIT/Apache and tool doesn't look in subfolders
		"name": "toml_datetime",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/toml-rs/toml/main/crates/toml_datetime/LICENSE-MIT"
	},
	{ // License is MIT/Apache and tool doesn't look in subfolders
		"name": "dirs-sys-next",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/xdg-rs/dirs/master/dirs-sys/LICENSE-MIT"
	},
	{ // License is MIT/Apache and gitlab API doesn't find the project
		"name": "libredox",
		"fullLicenseTextUri": "https://gitlab.redox-os.org/redox-os/libredox/-/raw/master/LICENSE"
	},
	{
		"name": "https-proxy-agent",
		"fullLicenseText": [
			"(The MIT License)",
			"Copyright (c) 2013 Nathan Rajlich <<EMAIL>>",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the 'Software'), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "data-uri-to-buffer",
		"fullLicenseText": [
			"(The MIT License)",
			"Copyright (c) 2014 Nathan Rajlich <<EMAIL>>",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the 'Software'), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "socks-proxy-agent",
		"fullLicenseText": [
			"(The MIT License)",
			"Copyright (c) 2013 Nathan Rajlich <<EMAIL>>",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the 'Software'), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "http-proxy-agent",
		"fullLicenseText": [
			"(The MIT License)",
			"Copyright (c) 2013 Nathan Rajlich <<EMAIL>>",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the 'Software'), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "agent-base",
		"fullLicenseText": [
			"(The MIT License)",
			"Copyright (c) 2013 Nathan Rajlich <<EMAIL>>",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the 'Software'), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:",
			"The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.",
			"THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "anstyle",
		"fullLicenseText": [
			"This software is released under the MIT license:",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of",
			"this software and associated documentation files (the \"Software\"), to deal in",
			"the Software without restriction, including without limitation the rights to",
			"use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of",
			"the Software, and to permit persons to whom the Software is furnished to do so,",
			"subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS",
			"FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR",
			"COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER",
			"IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN",
			"CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "anstyle-query",
		"fullLicenseText": [
			"This software is released under the MIT license:",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of",
			"this software and associated documentation files (the \"Software\"), to deal in",
			"the Software without restriction, including without limitation the rights to",
			"use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of",
			"the Software, and to permit persons to whom the Software is furnished to do so,",
			"subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS",
			"FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR",
			"COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER",
			"IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN",
			"CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "anstyle-parse",
		"fullLicenseText": [
			"This software is released under the MIT license:",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of",
			"this software and associated documentation files (the \"Software\"), to deal in",
			"the Software without restriction, including without limitation the rights to",
			"use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of",
			"the Software, and to permit persons to whom the Software is furnished to do so,",
			"subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS",
			"FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR",
			"COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER",
			"IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN",
			"CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "anstyle-wincon",
		"fullLicenseText": [
			"This software is released under the MIT license:",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of",
			"this software and associated documentation files (the \"Software\"), to deal in",
			"the Software without restriction, including without limitation the rights to",
			"use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of",
			"the Software, and to permit persons to whom the Software is furnished to do so,",
			"subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS",
			"FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR",
			"COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER",
			"IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN",
			"CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "anstream",
		"fullLicenseText": [
			"This software is released under the MIT license:",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of",
			"this software and associated documentation files (the \"Software\"), to deal in",
			"the Software without restriction, including without limitation the rights to",
			"use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of",
			"the Software, and to permit persons to whom the Software is furnished to do so,",
			"subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS",
			"FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR",
			"COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER",
			"IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN",
			"CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "colorchoice",
		"fullLicenseText": [
			"This software is released under the MIT license:",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy of",
			"this software and associated documentation files (the \"Software\"), to deal in",
			"the Software without restriction, including without limitation the rights to",
			"use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of",
			"the Software, and to permit persons to whom the Software is furnished to do so,",
			"subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS",
			"FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR",
			"COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER",
			"IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN",
			"CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."
		]
	},
	{
		"name": "cacheable-request",
		"prependLicenseText": [
			"Copyright (c) cacheable-request authors"
		]
	},
	{
		"name": "@vscode/ts-package-manager",
		"fullLicenseText": [
			"MIT License",
			"",
			"Copyright (c) Microsoft Corporation.",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy",
			"of this software and associated documentation files (the \"Software\"), to deal",
			"in the Software without restriction, including without limitation the rights",
			"to use, copy, modify, merge, publish, distribute, sublicense, and/or sell",
			"copies of the Software, and to permit persons to whom the Software is",
			"furnished to do so, subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,",
			"FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE",
			"AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER",
			"LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,",
			"OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE",
			"SOFTWARE"
		]
	},
	{
		"name":"vscode-markdown-languageserver",
		"fullLicenseText": [
			"MIT License",
			"",
			"Copyright (c) Microsoft Corporation.",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy",
			"of this software and associated documentation files (the \"Software\"), to deal",
			"in the Software without restriction, including without limitation the rights",
			"to use, copy, modify, merge, publish, distribute, sublicense, and/or sell",
			"copies of the Software, and to permit persons to whom the Software is",
			"furnished to do so, subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,",
			"FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE",
			"AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER",
			"LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,",
			"OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE",
			"SOFTWARE"
		]
	},
	{
		// Reason: mono-repo where the individual packages are also dual-licensed under MIT and Apache-2.0
		"name": "system-configuration",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/mullvad/system-configuration-rs/v0.6.0/system-configuration/LICENSE-MIT"
	},
	{
		// Reason: mono-repo where the individual packages are also dual-licensed under MIT and Apache-2.0
		"name": "system-configuration-sys",
		"fullLicenseTextUri": "https://raw.githubusercontent.com/mullvad/system-configuration-rs/v0.6.0/system-configuration-sys/LICENSE-MIT"
	},
	{
		// Reason: License missing from the repository https://github.com/isaacs/chownr/issues/35
		"name": "chownr",
		"fullLicenseText": [
			"The ISC License",
			"Copyright (c) Isaac Z. Schlueter and Contributors",
			"Permission to use, copy, modify, and/or distribute this software for any",
			"purpose with or without fee is hereby granted, provided that the above",
			"copyright notice and this permission notice appear in all copies.",
			"THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES",
			"WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF",
			"MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR",
			"ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES",
			"WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN",
			"ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR",
			"IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE."
		]
	},
	{
		"name": "@azure/msal-node-runtime",
		"fullLicenseText": [
			"MIT License",
			"",
			"Copyright (c) Microsoft Corporation. All rights reserved.",
			"",
			"Permission is hereby granted, free of charge, to any person obtaining a copy",
			"of this software and associated documentation files (the \"Software\"), to deal",
			"in the Software without restriction, including without limitation the rights",
			"to use, copy, modify, merge, publish, distribute, sublicense, and/or sell",
			"copies of the Software, and to permit persons to whom the Software is",
			"furnished to do so, subject to the following conditions:",
			"",
			"The above copyright notice and this permission notice shall be included in all",
			"copies or substantial portions of the Software.",
			"",
			"THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR",
			"IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,",
			"FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE",
			"AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER",
			"LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,",
			"OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE",
			"SOFTWARE"
		]
	}
]
