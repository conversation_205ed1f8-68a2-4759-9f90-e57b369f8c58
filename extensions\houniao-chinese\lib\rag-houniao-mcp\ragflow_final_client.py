#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow 最终客户端
专门针对候鸟浏览器数据集的查询客户端
"""

import json
import requests
import time
from typing import Dict, Any, Optional, List

class RAGFlowFinalClient:
    """RAGFlow 最终客户端 - 专门查询候鸟浏览器数据集"""
    
    def __init__(self, 
                 ragflow_url: str = "http://************:9380",
                 api_key: str = "ragflow-UzYTlhMjIwNjI0YzExZjA4NzUyMDI0Mm"):
        self.ragflow_url = ragflow_url.rstrip('/')
        self.api_key = api_key
        
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "RAGFlow-Final-Client/1.0",
            "Accept": "application/json"
        }
        
        # 候鸟浏览器数据集ID（从测试中发现）
        self.houniao_dataset_id = "6d7b2882624511f09a0d0242ac130006"
        self.test_dataset_id = "7aecdc1e6b0911f0996b0242ac130003"
        
        print(f"🎯 RAGFlow 最终客户端 - 候鸟浏览器专用")
        print(f"   服务器: {self.ragflow_url}")
        print(f"   候鸟数据集ID: {self.houniao_dataset_id}")
    
    def query_houniao_dataset(self, question: str, top_k: int = 5) -> Optional[Dict[str, Any]]:
        """查询候鸟浏览器数据集"""
        print(f"\n🔍 查询候鸟浏览器数据集: {question}")
        
        try:
            url = f"{self.ragflow_url}/api/v1/retrieval"
            
            # 只使用候鸟浏览器数据集，避免嵌入模型冲突
            request_data = {
                "question": question,
                "dataset_ids": [self.houniao_dataset_id],  # 只使用候鸟数据集
                "top_k": top_k
            }
            
            print(f"   请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
            
            response = requests.post(url, headers=self.headers, json=request_data, timeout=30)
            
            print(f"   状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get("code") == 0:
                    print("✅ 候鸟数据集查询成功!")
                    
                    # 解析和显示结果
                    data = result.get("data", {})
                    chunks = data.get("chunks", [])
                    
                    print(f"   找到 {len(chunks)} 个相关文档块:")

                    for i, chunk in enumerate(chunks[:2], 1):  # 显示前2个结果
                        # 获取内容，优先使用 content，然后是 content_with_weight
                        content = chunk.get("content", "") or chunk.get("content_with_weight", "")
                        score = chunk.get("similarity", 0)

                        print(f"\n   📄 结果 {i} (相似度: {score:.3f}):")

                        if content:
                            # 清理内容格式
                            content_clean = content.replace('\r\n', '\n').replace('\r', '\n')
                            # 提取主要内容部分
                            if '内容 ====' in content_clean:
                                main_content = content_clean.split('内容 ====')[1].split('英文内容')[0]
                            else:
                                main_content = content_clean

                            # 显示前800字符
                            if len(main_content) > 800:
                                display_content = main_content[:800] + "..."
                            else:
                                display_content = main_content

                            # 格式化显示，每行缩进
                            lines = display_content.split('\n')
                            for line in lines[:20]:  # 最多显示20行
                                if line.strip():
                                    print(f"      {line.strip()}")
                        else:
                            print(f"      [内容为空]")
                        print(f"      " + "─" * 80)
                    
                    return result
                else:
                    print(f"❌ 查询失败: {result.get('message', 'Unknown error')}")
                    print(f"   完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 查询异常: {e}")
            return None
    
    def query_test_dataset(self, question: str, top_k: int = 5) -> Optional[Dict[str, Any]]:
        """查询测试数据集"""
        print(f"\n🧪 查询测试数据集: {question}")
        
        try:
            url = f"{self.ragflow_url}/api/v1/retrieval"
            
            request_data = {
                "question": question,
                "dataset_ids": [self.test_dataset_id],  # 只使用测试数据集
                "top_k": top_k
            }
            
            response = requests.post(url, headers=self.headers, json=request_data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get("code") == 0:
                    print("✅ 测试数据集查询成功!")
                    return result
                else:
                    print(f"❌ 查询失败: {result.get('message', 'Unknown error')}")
                    return None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 查询异常: {e}")
            return None
    
    def comprehensive_test(self) -> Dict[str, Any]:
        """综合测试"""
        print(f"\n🚀 候鸟浏览器知识库综合测试")
        print("=" * 60)
        
        # 候鸟浏览器相关的测试问题
        test_questions = [
            "候鸟浏览器如何配置代理？",
            "候鸟浏览器怎么设置代理服务器？",
            "如何在候鸟浏览器中使用代理？",
            "候鸟浏览器代理配置步骤",
            "候鸟浏览器的功能有哪些？"
        ]
        
        results = {
            "houniao_dataset_results": [],
            "test_dataset_results": [],
            "successful_queries": 0,
            "total_queries": len(test_questions)
        }
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n{'='*15} 测试 {i}/{len(test_questions)} {'='*15}")
            
            # 查询候鸟数据集
            houniao_result = self.query_houniao_dataset(question)
            results["houniao_dataset_results"].append({
                "question": question,
                "result": houniao_result,
                "success": houniao_result is not None
            })
            
            if houniao_result:
                results["successful_queries"] += 1
            
            # 添加延迟避免请求过快
            if i < len(test_questions):
                time.sleep(1)
        
        # 生成报告
        self.generate_final_report(results)
        
        return results
    
    def generate_final_report(self, results: Dict[str, Any]):
        """生成最终报告"""
        print(f"\n" + "=" * 60)
        print(f"📋 候鸟浏览器知识库测试报告")
        print(f"=" * 60)
        
        success_rate = (results["successful_queries"] / results["total_queries"]) * 100
        
        print(f"📊 测试统计:")
        print(f"   总查询数: {results['total_queries']}")
        print(f"   成功查询: {results['successful_queries']}")
        print(f"   成功率: {success_rate:.1f}%")
        
        print(f"\n✅ 成功的查询:")
        for item in results["houniao_dataset_results"]:
            if item["success"]:
                print(f"   - {item['question']}")
        
        print(f"\n❌ 失败的查询:")
        for item in results["houniao_dataset_results"]:
            if not item["success"]:
                print(f"   - {item['question']}")
        
        if results["successful_queries"] > 0:
            print(f"\n🎉 候鸟浏览器知识库可以正常使用!")
            print(f"💡 建议:")
            print(f"   - 在 Wing 客户端中集成候鸟数据集查询功能")
            print(f"   - 使用数据集ID: {self.houniao_dataset_id}")
            print(f"   - 避免同时查询多个使用不同嵌入模型的数据集")
        else:
            print(f"\n⚠️  候鸟浏览器知识库查询存在问题")
            print(f"💡 建议:")
            print(f"   - 检查数据集配置")
            print(f"   - 确认嵌入模型设置")
            print(f"   - 联系 RAGFlow 管理员")
    
    def generate_wing_go_client(self) -> str:
        """生成 Wing 客户端的 GO 代码"""
        print(f"\n🔧 生成 Wing 客户端 GO 代码...")
        
        go_code = f'''package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "time"
)

// HouniaoRAGFlowClient 候鸟浏览器专用 RAGFlow 客户端
type HouniaoRAGFlowClient struct {{
    BaseURL          string
    APIKey           string
    HouniaoDatasetID string
    Client           *http.Client
}}

// NewHouniaoRAGFlowClient 创建候鸟浏览器专用客户端
func NewHouniaoRAGFlowClient() *HouniaoRAGFlowClient {{
    return &HouniaoRAGFlowClient{{
        BaseURL:          "{self.ragflow_url}",
        APIKey:           "{self.api_key}",
        HouniaoDatasetID: "{self.houniao_dataset_id}",
        Client: &http.Client{{
            Timeout: 30 * time.Second,
        }},
    }}
}}

// RetrievalRequest 检索请求结构
type RetrievalRequest struct {{
    Question   string   `json:"question"`
    DatasetIDs []string `json:"dataset_ids"`
    TopK       int      `json:"top_k"`
}}

// RetrievalResponse 检索响应结构
type RetrievalResponse struct {{
    Code int `json:"code"`
    Data struct {{
        Chunks []struct {{
            ContentWithWeight string  `json:"content_with_weight"`
            Similarity        float64 `json:"similarity"`
        }} `json:"chunks"`
    }} `json:"data"`
    Message string `json:"message"`
}}

// QueryHouniaoKnowledgeBase 查询候鸟浏览器知识库
func (c *HouniaoRAGFlowClient) QueryHouniaoKnowledgeBase(question string) (string, error) {{
    url := fmt.Sprintf("%s/api/v1/retrieval", c.BaseURL)
    
    reqData := RetrievalRequest{{
        Question:   question,
        DatasetIDs: []string{{c.HouniaoDatasetID}}, // 只使用候鸟数据集
        TopK:       5,
    }}
    
    jsonData, err := json.Marshal(reqData)
    if err != nil {{
        return "", fmt.Errorf("序列化请求失败: %v", err)
    }}
    
    req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
    if err != nil {{
        return "", fmt.Errorf("创建请求失败: %v", err)
    }}
    
    req.Header.Set("Authorization", "Bearer "+c.APIKey)
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Accept", "application/json")
    
    resp, err := c.Client.Do(req)
    if err != nil {{
        return "", fmt.Errorf("发送请求失败: %v", err)
    }}
    defer resp.Body.Close()
    
    body, err := io.ReadAll(resp.Body)
    if err != nil {{
        return "", fmt.Errorf("读取响应失败: %v", err)
    }}
    
    if resp.StatusCode != http.StatusOK {{
        return "", fmt.Errorf("HTTP错误 %d: %s", resp.StatusCode, string(body))
    }}
    
    var result RetrievalResponse
    if err := json.Unmarshal(body, &result); err != nil {{
        return "", fmt.Errorf("解析响应失败: %v", err)
    }}
    
    if result.Code != 0 {{
        return "", fmt.Errorf("查询失败: %s", result.Message)
    }}
    
    // 提取最相关的答案
    if len(result.Data.Chunks) > 0 {{
        bestMatch := result.Data.Chunks[0]
        return fmt.Sprintf("根据候鸟浏览器知识库查询结果：\\n\\n%s\\n\\n(相似度: %.3f)", 
            bestMatch.ContentWithWeight, bestMatch.Similarity), nil
    }}
    
    return "未找到相关信息", nil
}}

// TestConnection 测试连接
func (c *HouniaoRAGFlowClient) TestConnection() error {{
    result, err := c.QueryHouniaoKnowledgeBase("测试连接")
    if err != nil {{
        return fmt.Errorf("连接测试失败: %v", err)
    }}
    
    fmt.Printf("✅ 候鸟浏览器知识库连接成功\\n")
    fmt.Printf("📄 测试查询结果: %s\\n", result[:100]+"...")
    return nil
}}

// 在 Wing 客户端中的使用示例
func main() {{
    client := NewHouniaoRAGFlowClient()
    
    // 测试连接
    if err := client.TestConnection(); err != nil {{
        fmt.Printf("❌ 连接失败: %v\\n", err)
        return
    }}
    
    // 查询候鸟浏览器相关问题
    questions := []string{{
        "候鸟浏览器如何配置代理？",
        "候鸟浏览器的功能有哪些？",
        "如何使用候鸟浏览器？",
    }}
    
    for _, question := range questions {{
        fmt.Printf("\\n🔍 查询: %s\\n", question)
        
        answer, err := client.QueryHouniaoKnowledgeBase(question)
        if err != nil {{
            fmt.Printf("❌ 查询失败: %v\\n", err)
            continue
        }}
        
        fmt.Printf("🤖 答案: %s\\n", answer)
        fmt.Println(strings.Repeat("-", 60))
    }}
}}'''
        
        # 保存代码
        with open("houniao_ragflow_client.go", "w", encoding="utf-8") as f:
            f.write(go_code)
        
        print(f"✅ 候鸟浏览器专用 GO 客户端已生成: houniao_ragflow_client.go")
        return go_code


def main():
    """主函数"""
    client = RAGFlowFinalClient()
    
    try:
        # 运行综合测试
        results = client.comprehensive_test()
        
        # 生成 Wing 客户端代码
        client.generate_wing_go_client()
        
        # 保存结果
        with open("houniao_final_test_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试结果已保存到: houniao_final_test_results.json")
        print(f"🔧 Wing 客户端代码: houniao_ragflow_client.go")
        
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
