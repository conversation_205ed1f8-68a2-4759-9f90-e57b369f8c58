var tn=(k,N)=>()=>(N||k((N={exports:{}}).exports,N),N.exports);var rn=tn((ge,Tt)=>{(function(N,H){typeof ge=="object"&&typeof Tt=="object"?Tt.exports=H():typeof define=="function"&&define.amd?define([],H):typeof ge=="object"?ge.katex=H():N.katex=H()})(typeof self<"u"?self:ge,function(){return function(){"use strict";var k={};(function(){k.d=function(t,e){for(var r in e)k.o(e,r)&&!k.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}})(),function(){k.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}();var N={};k.d(N,{default:function(){return U1}});class H{constructor(e,r){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;let n="KaTeX parse error: "+e,s,a,o=r&&r.loc;if(o&&o.start<=o.end){let m=o.lexer.input;s=o.start,a=o.end,s===m.length?n+=" at end of input: ":n+=" at position "+(s+1)+": ";let f=m.slice(s,a).replace(/[^]/g,"$&\u0332"),b;s>15?b="\u2026"+m.slice(s-15,s):b=m.slice(0,s);let y;a+15<m.length?y=m.slice(a,a+15)+"\u2026":y=m.slice(a),n+=b+f+y}let u=new Error(n);return u.name="ParseError",u.__proto__=H.prototype,u.position=s,s!=null&&a!=null&&(u.length=a-s),u.rawMessage=e,u}}H.prototype.__proto__=Error.prototype;var v=H;let L=function(t,e){return t.indexOf(e)!==-1},R=function(t,e){return t===void 0?e:t},P=/([A-Z])/g,r0=function(t){return t.replace(P,"-$1").toLowerCase()},o0={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},$=/[&><"']/g;function Q(t){return String(t).replace($,e=>o0[e])}let c0=function(t){return t.type==="ordgroup"||t.type==="color"?t.body.length===1?c0(t.body[0]):t:t.type==="font"?c0(t.body):t},k0=function(t){let e=c0(t);return e.type==="mathord"||e.type==="textord"||e.type==="atom"},j=function(t){if(!t)throw new Error("Expected non-null, but got "+String(t));return t};var T={contains:L,deflt:R,escape:Q,hyphenate:r0,getBaseElem:c0,isCharacterBox:k0,protocolFromUrl:function(t){let e=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(t);return e?e[2]!==":"||!/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(e[1])?null:e[1].toLowerCase():"_relative"}};let f0={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:t=>"#"+t},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(t,e)=>(e.push(t),e)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:t=>Math.max(0,t),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:t=>Math.max(0,t),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:t=>Math.max(0,t),cli:"-e, --max-expand <n>",cliProcessor:t=>t==="Infinity"?1/0:parseInt(t)},globalGroup:{type:"boolean",cli:!1}};function le(t){if(t.default)return t.default;let e=t.type,r=Array.isArray(e)?e[0]:e;if(typeof r!="string")return r.enum[0];switch(r){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}class oe{constructor(e){this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{};for(let r in f0)if(f0.hasOwnProperty(r)){let n=f0[r];this[r]=e[r]!==void 0?n.processor?n.processor(e[r]):e[r]:le(n)}}reportNonstrict(e,r,n){let s=this.strict;if(typeof s=="function"&&(s=s(e,r,n)),!(!s||s==="ignore")){if(s===!0||s==="error")throw new v("LaTeX-incompatible input and strict mode is set to 'error': "+(r+" ["+e+"]"),n);s==="warn"?typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(r+" ["+e+"]")):typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+s+"': "+r+" ["+e+"]"))}}useStrictBehavior(e,r,n){let s=this.strict;if(typeof s=="function")try{s=s(e,r,n)}catch{s="error"}return!s||s==="ignore"?!1:s===!0||s==="error"?!0:s==="warn"?(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(r+" ["+e+"]")),!1):(typeof console<"u"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+s+"': "+r+" ["+e+"]")),!1)}isTrusted(e){if(e.url&&!e.protocol){let n=T.protocolFromUrl(e.url);if(n==null)return!1;e.protocol=n}return!!(typeof this.trust=="function"?this.trust(e):this.trust)}}class q0{constructor(e,r,n){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=r,this.cramped=n}sup(){return E0[hn[this.id]]}sub(){return E0[mn[this.id]]}fracNum(){return E0[dn[this.id]]}fracDen(){return E0[pn[this.id]]}cramp(){return E0[fn[this.id]]}text(){return E0[gn[this.id]]}isTight(){return this.size>=2}}let ee=0,ye=1,te=2,F0=3,ce=4,z0=5,re=6,g0=7,E0=[new q0(ee,0,!1),new q0(ye,0,!0),new q0(te,1,!1),new q0(F0,1,!0),new q0(ce,2,!1),new q0(z0,2,!0),new q0(re,3,!1),new q0(g0,3,!0)],hn=[ce,z0,ce,z0,re,g0,re,g0],mn=[z0,z0,z0,z0,g0,g0,g0,g0],dn=[te,F0,ce,z0,re,g0,re,g0],pn=[F0,F0,z0,z0,g0,g0,g0,g0],fn=[ye,ye,F0,F0,z0,z0,g0,g0],gn=[ee,ye,te,F0,te,F0,te,F0];var E={DISPLAY:E0[ee],TEXT:E0[te],SCRIPT:E0[ce],SCRIPTSCRIPT:E0[re]};let Pe=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];function bn(t){for(let e=0;e<Pe.length;e++){let r=Pe[e];for(let n=0;n<r.blocks.length;n++){let s=r.blocks[n];if(t>=s[0]&&t<=s[1])return r.name}}return null}let xe=[];Pe.forEach(t=>t.blocks.forEach(e=>xe.push(...e)));function Bt(t){for(let e=0;e<xe.length;e+=2)if(t>=xe[e]&&t<=xe[e+1])return!0;return!1}let ne=80,yn=function(t,e){return"M95,"+(622+t+e)+`
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l`+t/2.075+" -"+t+`
c5.3,-9.3,12,-14,20,-14
H400000v`+(40+t)+`H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M`+(834+t)+" "+e+"h400000v"+(40+t)+"h-400000z"},xn=function(t,e){return"M263,"+(601+t+e)+`c0.7,0,18,39.7,52,119
c34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120
c340,-704.7,510.7,-1060.3,512,-1067
l`+t/2.084+" -"+t+`
c4.7,-7.3,11,-11,19,-11
H40000v`+(40+t)+`H1012.3
s-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232
c-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1
s-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26
c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z
M`+(1001+t)+" "+e+"h400000v"+(40+t)+"h-400000z"},wn=function(t,e){return"M983 "+(10+t+e)+`
l`+t/3.13+" -"+t+`
c4,-6.7,10,-10,18,-10 H400000v`+(40+t)+`
H1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7
s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744
c-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30
c26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722
c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5
c53.7,-170.3,84.5,-266.8,92.5,-289.5z
M`+(1001+t)+" "+e+"h400000v"+(40+t)+"h-400000z"},kn=function(t,e){return"M424,"+(2398+t+e)+`
c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514
c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20
s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121
s209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081
l`+t/4.223+" -"+t+`c4,-6.7,10,-10,18,-10 H400000
v`+(40+t)+`H1014.6
s-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185
c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2z M`+(1001+t)+" "+e+`
h400000v`+(40+t)+"h-400000z"},vn=function(t,e){return"M473,"+(2713+t+e)+`
c339.3,-1799.3,509.3,-2700,510,-2702 l`+t/5.298+" -"+t+`
c3.3,-7.3,9.3,-11,18,-11 H400000v`+(40+t)+`H1017.7
s-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200
c0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26
s76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,
606zM`+(1001+t)+" "+e+"h400000v"+(40+t)+"H1017.7z"},Sn=function(t){let e=t/2;return"M400000 "+t+" H0 L"+e+" 0 l65 45 L145 "+(t-80)+" H400000z"},Mn=function(t,e,r){let n=r-54-e-t;return"M702 "+(t+e)+"H400000"+(40+t)+`
H742v`+n+`l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1
h-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170
c-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667
219 661 l218 661zM702 `+e+"H400000v"+(40+t)+"H742z"},zn=function(t,e,r){e=1e3*e;let n="";switch(t){case"sqrtMain":n=yn(e,ne);break;case"sqrtSize1":n=xn(e,ne);break;case"sqrtSize2":n=wn(e,ne);break;case"sqrtSize3":n=kn(e,ne);break;case"sqrtSize4":n=vn(e,ne);break;case"sqrtTall":n=Mn(e,ne,r)}return n},An=function(t,e){switch(t){case"\u239C":return"M291 0 H417 V"+e+" H291z M291 0 H417 V"+e+" H291z";case"\u2223":return"M145 0 H188 V"+e+" H145z M145 0 H188 V"+e+" H145z";case"\u2225":return"M145 0 H188 V"+e+" H145z M145 0 H188 V"+e+" H145z"+("M367 0 H410 V"+e+" H367z M367 0 H410 V"+e+" H367z");case"\u239F":return"M457 0 H583 V"+e+" H457z M457 0 H583 V"+e+" H457z";case"\u23A2":return"M319 0 H403 V"+e+" H319z M319 0 H403 V"+e+" H319z";case"\u23A5":return"M263 0 H347 V"+e+" H263z M263 0 H347 V"+e+" H263z";case"\u23AA":return"M384 0 H504 V"+e+" H384z M384 0 H504 V"+e+" H384z";case"\u23D0":return"M312 0 H355 V"+e+" H312z M312 0 H355 V"+e+" H312z";case"\u2016":return"M257 0 H300 V"+e+" H257z M257 0 H300 V"+e+" H257z"+("M478 0 H521 V"+e+" H478z M478 0 H521 V"+e+" H478z");default:return""}},Nt={doubleleftarrow:`M262 157
l10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3
 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28
 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5
c2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5
 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87
-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7
-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z
m8 0v40h399730v-40zm0 194v40h399730v-40z`,doublerightarrow:`M399738 392l
-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5
 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88
-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68
-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18
-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782
c-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3
-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z`,leftarrow:`M400000 241H110l3-3c68.7-52.7 113.7-120
 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8
-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247
c-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208
 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3
 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202
 l-3-3h399890zM100 241v40h399900v-40z`,leftbrace:`M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117
-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7
 5-6 9-10 13-.7 1-7.3 1-20 1H6z`,leftbraceunder:`M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13
 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688
 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7
-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z`,leftgroup:`M400000 80
H435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0
 435 0h399565z`,leftgroupunder:`M400000 262
H435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219
 435 219h399565z`,leftharpoon:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3
-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5
-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7
-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z`,leftharpoonplus:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5
 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3
-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7
-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z
m0 0v40h400000v-40z`,leftharpoondown:`M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333
 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5
 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667
-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z`,leftharpoondownplus:`M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12
 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7
-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0
v40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z`,lefthook:`M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5
-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3
-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21
 71.5 23h399859zM103 281v-40h399897v40z`,leftlinesegment:`M40 281 V428 H0 V94 H40 V241 H400000 v40z
M40 281 V428 H0 V94 H40 V241 H400000 v40z`,leftmapsto:`M40 281 V448H0V74H40V241H400000v40z
M40 281 V448H0V74H40V241H400000v40z`,leftToFrom:`M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23
-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8
c28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3
 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z`,longequal:`M0 50 h400000 v40H0z m0 194h40000v40H0z
M0 50 h400000 v40H0z m0 194h40000v40H0z`,midbrace:`M200428 334
c-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14
-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7
 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11
 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z`,midbraceunder:`M199572 214
c100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14
 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3
 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0
-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z`,oiintSize1:`M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6
-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z
m368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8
60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z`,oiintSize2:`M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8
-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z
m502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2
c0 110 84 276 504 276s502.4-166 502.4-276z`,oiiintSize1:`M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6
-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z
m525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0
85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z`,oiiintSize2:`M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8
-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z
m770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1
c0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z`,rightarrow:`M0 241v40h399891c-47.3 35.3-84 78-110 128
-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20
 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7
 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85
-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
 151.7 139 205zm0 0v40h399900v-40z`,rightbrace:`M400000 542l
-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5
s-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1
c124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z`,rightbraceunder:`M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3
 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237
-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z`,rightgroup:`M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0
 3-1 3-3v-38c-76-158-257-219-435-219H0z`,rightgroupunder:`M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18
 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z`,rightharpoon:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3
-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2
-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58
 69.2 92 94.5zm0 0v40h399900v-40z`,rightharpoonplus:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11
-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7
 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z
m0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z`,rightharpoondown:`M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8
 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5
-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95
-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z`,rightharpoondownplus:`M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8
 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3
 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3
-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z
m0-194v40h400000v-40zm0 0v40h400000v-40z`,righthook:`M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3
 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0
-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21
 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z`,rightlinesegment:`M399960 241 V94 h40 V428 h-40 V281 H0 v-40z
M399960 241 V94 h40 V428 h-40 V281 H0 v-40z`,rightToFrom:`M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23
 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32
-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142
-167z M100 147v40h399900v-40zM0 341v40h399900v-40z`,twoheadleftarrow:`M0 167c68 40
 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69
-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3
-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19
-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101
 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z`,twoheadrightarrow:`M400000 167
c-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3
 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42
 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333
-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70
 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z`,tilde1:`M200 55.538c-77 0-168 73.953-177 73.953-3 0-7
-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0
 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0
 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128
-68.267.847-113-73.952-191-73.952z`,tilde2:`M344 55.266c-142 0-300.638 81.316-311.5 86.418
-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9
 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114
c1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751
 181.476 676 181.476c-149 0-189-126.21-332-126.21z`,tilde3:`M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457
-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0
 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697
 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696
 -338 0-409-156.573-744-156.573z`,tilde4:`M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345
-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409
 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9
 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409
 -175.236-744-175.236z`,vec:`M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5
3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11
10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63
-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1
-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59
H213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359
c-16-25.333-24-45-24-59z`,widehat1:`M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22
c-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z`,widehat2:`M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat3:`M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat4:`M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widecheck1:`M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,
-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z`,widecheck2:`M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck3:`M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck4:`M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,baraboveleftarrow:`M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202
c4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5
c-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130
s-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47
121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6
s2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11
c0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z
M100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z`,rightarrowabovebar:`M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32
-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0
13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39
-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5
-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z`,baraboveshortleftharpoon:`M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17
c2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21
c-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40
c-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z
M0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z`,rightharpoonaboveshortbar:`M0,241 l0,40c399126,0,399993,0,399993,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z`,shortbaraboveleftharpoon:`M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,
1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,
-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z
M93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z`,shortrightharpoonabovebar:`M53,241l0,40c398570,0,399437,0,399437,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`},Tn=function(t,e){switch(t){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+e+` v1759 h347 v-84
H403z M403 1759 V0 H319 V1759 v`+e+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+e+` v1759 H0 v84 H347z
M347 1759 V0 H263 V1759 v`+e+" v1759 h84z";case"vert":return"M145 15 v585 v"+e+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-e+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+e+" v585 h43z";case"doublevert":return"M145 15 v585 v"+e+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-e+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+e+` v585 h43z
M367 15 v585 v`+e+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-e+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M410 15 H367 v585 v`+e+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+e+` v1715 h263 v84 H319z
MM319 602 V0 H403 V602 v`+e+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+e+` v1799 H0 v-84 H319z
MM319 602 V0 H403 V602 v`+e+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+e+` v602 h84z
M403 1759 V0 H319 V1759 v`+e+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+e+` v602 h84z
M347 1759 V0 h-84 V1759 v`+e+" v602 h84z";case"lparen":return`M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1
c-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,
-36,557 l0,`+(e+84)+`c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,
949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9
c0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,
-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189
l0,-`+(e+92)+`c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,
-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z`;case"rparen":return`M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,
63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5
c11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,`+(e+9)+`
c-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664
c-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11
c0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17
c242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558
l0,-`+(e+144)+`c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,
-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z`;default:throw new Error("Unknown stretchy delimiter.")}};class ue{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return T.contains(this.classes,e)}toNode(){let e=document.createDocumentFragment();for(let r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e}toMarkup(){let e="";for(let r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e}toText(){let e=r=>r.toText();return this.children.map(e).join("")}}var I0={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}};let we={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},Ct={\u00C5:"A",\u00D0:"D",\u00DE:"o",\u00E5:"a",\u00F0:"d",\u00FE:"o",\u0410:"A",\u0411:"B",\u0412:"B",\u0413:"F",\u0414:"A",\u0415:"E",\u0416:"K",\u0417:"3",\u0418:"N",\u0419:"N",\u041A:"K",\u041B:"N",\u041C:"M",\u041D:"H",\u041E:"O",\u041F:"N",\u0420:"P",\u0421:"C",\u0422:"T",\u0423:"y",\u0424:"O",\u0425:"X",\u0426:"U",\u0427:"h",\u0428:"W",\u0429:"W",\u042A:"B",\u042B:"X",\u042C:"B",\u042D:"3",\u042E:"X",\u042F:"R",\u0430:"a",\u0431:"b",\u0432:"a",\u0433:"r",\u0434:"y",\u0435:"e",\u0436:"m",\u0437:"e",\u0438:"n",\u0439:"n",\u043A:"n",\u043B:"n",\u043C:"m",\u043D:"n",\u043E:"o",\u043F:"n",\u0440:"p",\u0441:"c",\u0442:"o",\u0443:"y",\u0444:"b",\u0445:"x",\u0446:"n",\u0447:"n",\u0448:"w",\u0449:"w",\u044A:"a",\u044B:"m",\u044C:"a",\u044D:"e",\u044E:"m",\u044F:"r"};function Bn(t,e){I0[t]=e}function Ve(t,e,r){if(!I0[e])throw new Error("Font metrics not found for font: "+e+".");let n=t.charCodeAt(0),s=I0[e][n];if(!s&&t[0]in Ct&&(n=Ct[t[0]].charCodeAt(0),s=I0[e][n]),!s&&r==="text"&&Bt(n)&&(s=I0[e][77]),s)return{depth:s[0],height:s[1],italic:s[2],skew:s[3],width:s[4]}}let Ge={};function Nn(t){let e;if(t>=5?e=0:t>=3?e=1:e=2,!Ge[e]){let r=Ge[e]={cssEmPerMu:we.quad[e]/18};for(let n in we)we.hasOwnProperty(n)&&(r[n]=we[n][e])}return Ge[e]}let Cn=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],Dt=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],qt=function(t,e){return e.size<2?t:Cn[t-1][e.size-1]};class _0{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||_0.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=Dt[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){let r={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(let n in e)e.hasOwnProperty(n)&&(r[n]=e[n]);return new _0(r)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:qt(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:Dt[e-1]})}havingBaseStyle(e){e=e||this.style.text();let r=qt(_0.BASESIZE,e);return this.size===r&&this.textSize===_0.BASESIZE&&this.style===e?this:this.extend({style:e,size:r})}havingBaseSizing(){let e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==_0.BASESIZE?["sizing","reset-size"+this.size,"size"+_0.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=Nn(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}_0.BASESIZE=6;var Dn=_0;let $e={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:803/800,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:803/800},qn={ex:!0,em:!0,mu:!0},Et=function(t){return typeof t!="string"&&(t=t.unit),t in $e||t in qn||t==="ex"},n0=function(t,e){let r;if(t.unit in $e)r=$e[t.unit]/e.fontMetrics().ptPerEm/e.sizeMultiplier;else if(t.unit==="mu")r=e.fontMetrics().cssEmPerMu;else{let n;if(e.style.isTight()?n=e.havingStyle(e.style.text()):n=e,t.unit==="ex")r=n.fontMetrics().xHeight;else if(t.unit==="em")r=n.fontMetrics().quad;else throw new v("Invalid unit: '"+t.unit+"'");n!==e&&(r*=n.sizeMultiplier/e.sizeMultiplier)}return Math.min(t.number*r,e.maxSize)},A=function(t){return+t.toFixed(4)+"em"},Y0=function(t){return t.filter(e=>e).join(" ")},It=function(t,e,r){if(this.classes=t||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},e){e.style.isTight()&&this.classes.push("mtight");let n=e.getColor();n&&(this.style.color=n)}},Rt=function(t){let e=document.createElement(t);e.className=Y0(this.classes);for(let r in this.style)this.style.hasOwnProperty(r)&&(e.style[r]=this.style[r]);for(let r in this.attributes)this.attributes.hasOwnProperty(r)&&e.setAttribute(r,this.attributes[r]);for(let r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e},En=/[\s"'>/=\x00-\x1f]/,Ot=function(t){let e="<"+t;this.classes.length&&(e+=' class="'+T.escape(Y0(this.classes))+'"');let r="";for(let n in this.style)this.style.hasOwnProperty(n)&&(r+=T.hyphenate(n)+":"+this.style[n]+";");r&&(e+=' style="'+T.escape(r)+'"');for(let n in this.attributes)if(this.attributes.hasOwnProperty(n)){if(En.test(n))throw new v("Invalid attribute name '"+n+"'");e+=" "+n+'="'+T.escape(this.attributes[n])+'"'}e+=">";for(let n=0;n<this.children.length;n++)e+=this.children[n].toMarkup();return e+="</"+t+">",e};class he{constructor(e,r,n,s){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,It.call(this,e,n,s),this.children=r||[]}setAttribute(e,r){this.attributes[e]=r}hasClass(e){return T.contains(this.classes,e)}toNode(){return Rt.call(this,"span")}toMarkup(){return Ot.call(this,"span")}}class Ue{constructor(e,r,n,s){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,It.call(this,r,s),this.children=n||[],this.setAttribute("href",e)}setAttribute(e,r){this.attributes[e]=r}hasClass(e){return T.contains(this.classes,e)}toNode(){return Rt.call(this,"a")}toMarkup(){return Ot.call(this,"a")}}class In{constructor(e,r,n){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=r,this.src=e,this.classes=["mord"],this.style=n}hasClass(e){return T.contains(this.classes,e)}toNode(){let e=document.createElement("img");e.src=this.src,e.alt=this.alt,e.className="mord";for(let r in this.style)this.style.hasOwnProperty(r)&&(e.style[r]=this.style[r]);return e}toMarkup(){let e='<img src="'+T.escape(this.src)+'"'+(' alt="'+T.escape(this.alt)+'"'),r="";for(let n in this.style)this.style.hasOwnProperty(n)&&(r+=T.hyphenate(n)+":"+this.style[n]+";");return r&&(e+=' style="'+T.escape(r)+'"'),e+="'/>",e}}let Rn={\u00EE:"\u0131\u0302",\u00EF:"\u0131\u0308",\u00ED:"\u0131\u0301",\u00EC:"\u0131\u0300"};class A0{constructor(e,r,n,s,a,o,u,m){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=r||0,this.depth=n||0,this.italic=s||0,this.skew=a||0,this.width=o||0,this.classes=u||[],this.style=m||{},this.maxFontSize=0;let f=bn(this.text.charCodeAt(0));f&&this.classes.push(f+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=Rn[this.text])}hasClass(e){return T.contains(this.classes,e)}toNode(){let e=document.createTextNode(this.text),r=null;this.italic>0&&(r=document.createElement("span"),r.style.marginRight=A(this.italic)),this.classes.length>0&&(r=r||document.createElement("span"),r.className=Y0(this.classes));for(let n in this.style)this.style.hasOwnProperty(n)&&(r=r||document.createElement("span"),r.style[n]=this.style[n]);return r?(r.appendChild(e),r):e}toMarkup(){let e=!1,r="<span";this.classes.length&&(e=!0,r+=' class="',r+=T.escape(Y0(this.classes)),r+='"');let n="";this.italic>0&&(n+="margin-right:"+this.italic+"em;");for(let a in this.style)this.style.hasOwnProperty(a)&&(n+=T.hyphenate(a)+":"+this.style[a]+";");n&&(e=!0,r+=' style="'+T.escape(n)+'"');let s=T.escape(this.text);return e?(r+=">",r+=s,r+="</span>",r):s}}class P0{constructor(e,r){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=r||{}}toNode(){let r=document.createElementNS("http://www.w3.org/2000/svg","svg");for(let n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&r.setAttribute(n,this.attributes[n]);for(let n=0;n<this.children.length;n++)r.appendChild(this.children[n].toNode());return r}toMarkup(){let e='<svg xmlns="http://www.w3.org/2000/svg"';for(let r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&(e+=" "+r+'="'+T.escape(this.attributes[r])+'"');e+=">";for(let r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</svg>",e}}class X0{constructor(e,r){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=r}toNode(){let r=document.createElementNS("http://www.w3.org/2000/svg","path");return this.alternate?r.setAttribute("d",this.alternate):r.setAttribute("d",Nt[this.pathName]),r}toMarkup(){return this.alternate?'<path d="'+T.escape(this.alternate)+'"/>':'<path d="'+T.escape(Nt[this.pathName])+'"/>'}}class We{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){let r=document.createElementNS("http://www.w3.org/2000/svg","line");for(let n in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,n)&&r.setAttribute(n,this.attributes[n]);return r}toMarkup(){let e="<line";for(let r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&(e+=" "+r+'="'+T.escape(this.attributes[r])+'"');return e+="/>",e}}function Ht(t){if(t instanceof A0)return t;throw new Error("Expected symbolNode but got "+String(t)+".")}function On(t){if(t instanceof he)return t;throw new Error("Expected span<HtmlDomNode> but got "+String(t)+".")}let Hn={bin:1,close:1,inner:1,open:1,punct:1,rel:1},Ln={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},ke={math:{},text:{}};var s0=ke;function i(t,e,r,n,s,a){ke[t][s]={font:e,group:r,replace:n},a&&n&&(ke[t][n]=ke[t][s])}let l="math",M="text",c="main",d="ams",e0="accent-token",C="bin",b0="close",se="inner",I="mathord",m0="op-token",v0="open",ve="punct",p="rel",V0="spacing",g="textord";i(l,c,p,"\u2261","\\equiv",!0),i(l,c,p,"\u227A","\\prec",!0),i(l,c,p,"\u227B","\\succ",!0),i(l,c,p,"\u223C","\\sim",!0),i(l,c,p,"\u22A5","\\perp"),i(l,c,p,"\u2AAF","\\preceq",!0),i(l,c,p,"\u2AB0","\\succeq",!0),i(l,c,p,"\u2243","\\simeq",!0),i(l,c,p,"\u2223","\\mid",!0),i(l,c,p,"\u226A","\\ll",!0),i(l,c,p,"\u226B","\\gg",!0),i(l,c,p,"\u224D","\\asymp",!0),i(l,c,p,"\u2225","\\parallel"),i(l,c,p,"\u22C8","\\bowtie",!0),i(l,c,p,"\u2323","\\smile",!0),i(l,c,p,"\u2291","\\sqsubseteq",!0),i(l,c,p,"\u2292","\\sqsupseteq",!0),i(l,c,p,"\u2250","\\doteq",!0),i(l,c,p,"\u2322","\\frown",!0),i(l,c,p,"\u220B","\\ni",!0),i(l,c,p,"\u221D","\\propto",!0),i(l,c,p,"\u22A2","\\vdash",!0),i(l,c,p,"\u22A3","\\dashv",!0),i(l,c,p,"\u220B","\\owns"),i(l,c,ve,".","\\ldotp"),i(l,c,ve,"\u22C5","\\cdotp"),i(l,c,g,"#","\\#"),i(M,c,g,"#","\\#"),i(l,c,g,"&","\\&"),i(M,c,g,"&","\\&"),i(l,c,g,"\u2135","\\aleph",!0),i(l,c,g,"\u2200","\\forall",!0),i(l,c,g,"\u210F","\\hbar",!0),i(l,c,g,"\u2203","\\exists",!0),i(l,c,g,"\u2207","\\nabla",!0),i(l,c,g,"\u266D","\\flat",!0),i(l,c,g,"\u2113","\\ell",!0),i(l,c,g,"\u266E","\\natural",!0),i(l,c,g,"\u2663","\\clubsuit",!0),i(l,c,g,"\u2118","\\wp",!0),i(l,c,g,"\u266F","\\sharp",!0),i(l,c,g,"\u2662","\\diamondsuit",!0),i(l,c,g,"\u211C","\\Re",!0),i(l,c,g,"\u2661","\\heartsuit",!0),i(l,c,g,"\u2111","\\Im",!0),i(l,c,g,"\u2660","\\spadesuit",!0),i(l,c,g,"\xA7","\\S",!0),i(M,c,g,"\xA7","\\S"),i(l,c,g,"\xB6","\\P",!0),i(M,c,g,"\xB6","\\P"),i(l,c,g,"\u2020","\\dag"),i(M,c,g,"\u2020","\\dag"),i(M,c,g,"\u2020","\\textdagger"),i(l,c,g,"\u2021","\\ddag"),i(M,c,g,"\u2021","\\ddag"),i(M,c,g,"\u2021","\\textdaggerdbl"),i(l,c,b0,"\u23B1","\\rmoustache",!0),i(l,c,v0,"\u23B0","\\lmoustache",!0),i(l,c,b0,"\u27EF","\\rgroup",!0),i(l,c,v0,"\u27EE","\\lgroup",!0),i(l,c,C,"\u2213","\\mp",!0),i(l,c,C,"\u2296","\\ominus",!0),i(l,c,C,"\u228E","\\uplus",!0),i(l,c,C,"\u2293","\\sqcap",!0),i(l,c,C,"\u2217","\\ast"),i(l,c,C,"\u2294","\\sqcup",!0),i(l,c,C,"\u25EF","\\bigcirc",!0),i(l,c,C,"\u2219","\\bullet",!0),i(l,c,C,"\u2021","\\ddagger"),i(l,c,C,"\u2240","\\wr",!0),i(l,c,C,"\u2A3F","\\amalg"),i(l,c,C,"&","\\And"),i(l,c,p,"\u27F5","\\longleftarrow",!0),i(l,c,p,"\u21D0","\\Leftarrow",!0),i(l,c,p,"\u27F8","\\Longleftarrow",!0),i(l,c,p,"\u27F6","\\longrightarrow",!0),i(l,c,p,"\u21D2","\\Rightarrow",!0),i(l,c,p,"\u27F9","\\Longrightarrow",!0),i(l,c,p,"\u2194","\\leftrightarrow",!0),i(l,c,p,"\u27F7","\\longleftrightarrow",!0),i(l,c,p,"\u21D4","\\Leftrightarrow",!0),i(l,c,p,"\u27FA","\\Longleftrightarrow",!0),i(l,c,p,"\u21A6","\\mapsto",!0),i(l,c,p,"\u27FC","\\longmapsto",!0),i(l,c,p,"\u2197","\\nearrow",!0),i(l,c,p,"\u21A9","\\hookleftarrow",!0),i(l,c,p,"\u21AA","\\hookrightarrow",!0),i(l,c,p,"\u2198","\\searrow",!0),i(l,c,p,"\u21BC","\\leftharpoonup",!0),i(l,c,p,"\u21C0","\\rightharpoonup",!0),i(l,c,p,"\u2199","\\swarrow",!0),i(l,c,p,"\u21BD","\\leftharpoondown",!0),i(l,c,p,"\u21C1","\\rightharpoondown",!0),i(l,c,p,"\u2196","\\nwarrow",!0),i(l,c,p,"\u21CC","\\rightleftharpoons",!0),i(l,d,p,"\u226E","\\nless",!0),i(l,d,p,"\uE010","\\@nleqslant"),i(l,d,p,"\uE011","\\@nleqq"),i(l,d,p,"\u2A87","\\lneq",!0),i(l,d,p,"\u2268","\\lneqq",!0),i(l,d,p,"\uE00C","\\@lvertneqq"),i(l,d,p,"\u22E6","\\lnsim",!0),i(l,d,p,"\u2A89","\\lnapprox",!0),i(l,d,p,"\u2280","\\nprec",!0),i(l,d,p,"\u22E0","\\npreceq",!0),i(l,d,p,"\u22E8","\\precnsim",!0),i(l,d,p,"\u2AB9","\\precnapprox",!0),i(l,d,p,"\u2241","\\nsim",!0),i(l,d,p,"\uE006","\\@nshortmid"),i(l,d,p,"\u2224","\\nmid",!0),i(l,d,p,"\u22AC","\\nvdash",!0),i(l,d,p,"\u22AD","\\nvDash",!0),i(l,d,p,"\u22EA","\\ntriangleleft"),i(l,d,p,"\u22EC","\\ntrianglelefteq",!0),i(l,d,p,"\u228A","\\subsetneq",!0),i(l,d,p,"\uE01A","\\@varsubsetneq"),i(l,d,p,"\u2ACB","\\subsetneqq",!0),i(l,d,p,"\uE017","\\@varsubsetneqq"),i(l,d,p,"\u226F","\\ngtr",!0),i(l,d,p,"\uE00F","\\@ngeqslant"),i(l,d,p,"\uE00E","\\@ngeqq"),i(l,d,p,"\u2A88","\\gneq",!0),i(l,d,p,"\u2269","\\gneqq",!0),i(l,d,p,"\uE00D","\\@gvertneqq"),i(l,d,p,"\u22E7","\\gnsim",!0),i(l,d,p,"\u2A8A","\\gnapprox",!0),i(l,d,p,"\u2281","\\nsucc",!0),i(l,d,p,"\u22E1","\\nsucceq",!0),i(l,d,p,"\u22E9","\\succnsim",!0),i(l,d,p,"\u2ABA","\\succnapprox",!0),i(l,d,p,"\u2246","\\ncong",!0),i(l,d,p,"\uE007","\\@nshortparallel"),i(l,d,p,"\u2226","\\nparallel",!0),i(l,d,p,"\u22AF","\\nVDash",!0),i(l,d,p,"\u22EB","\\ntriangleright"),i(l,d,p,"\u22ED","\\ntrianglerighteq",!0),i(l,d,p,"\uE018","\\@nsupseteqq"),i(l,d,p,"\u228B","\\supsetneq",!0),i(l,d,p,"\uE01B","\\@varsupsetneq"),i(l,d,p,"\u2ACC","\\supsetneqq",!0),i(l,d,p,"\uE019","\\@varsupsetneqq"),i(l,d,p,"\u22AE","\\nVdash",!0),i(l,d,p,"\u2AB5","\\precneqq",!0),i(l,d,p,"\u2AB6","\\succneqq",!0),i(l,d,p,"\uE016","\\@nsubseteqq"),i(l,d,C,"\u22B4","\\unlhd"),i(l,d,C,"\u22B5","\\unrhd"),i(l,d,p,"\u219A","\\nleftarrow",!0),i(l,d,p,"\u219B","\\nrightarrow",!0),i(l,d,p,"\u21CD","\\nLeftarrow",!0),i(l,d,p,"\u21CF","\\nRightarrow",!0),i(l,d,p,"\u21AE","\\nleftrightarrow",!0),i(l,d,p,"\u21CE","\\nLeftrightarrow",!0),i(l,d,p,"\u25B3","\\vartriangle"),i(l,d,g,"\u210F","\\hslash"),i(l,d,g,"\u25BD","\\triangledown"),i(l,d,g,"\u25CA","\\lozenge"),i(l,d,g,"\u24C8","\\circledS"),i(l,d,g,"\xAE","\\circledR"),i(M,d,g,"\xAE","\\circledR"),i(l,d,g,"\u2221","\\measuredangle",!0),i(l,d,g,"\u2204","\\nexists"),i(l,d,g,"\u2127","\\mho"),i(l,d,g,"\u2132","\\Finv",!0),i(l,d,g,"\u2141","\\Game",!0),i(l,d,g,"\u2035","\\backprime"),i(l,d,g,"\u25B2","\\blacktriangle"),i(l,d,g,"\u25BC","\\blacktriangledown"),i(l,d,g,"\u25A0","\\blacksquare"),i(l,d,g,"\u29EB","\\blacklozenge"),i(l,d,g,"\u2605","\\bigstar"),i(l,d,g,"\u2222","\\sphericalangle",!0),i(l,d,g,"\u2201","\\complement",!0),i(l,d,g,"\xF0","\\eth",!0),i(M,c,g,"\xF0","\xF0"),i(l,d,g,"\u2571","\\diagup"),i(l,d,g,"\u2572","\\diagdown"),i(l,d,g,"\u25A1","\\square"),i(l,d,g,"\u25A1","\\Box"),i(l,d,g,"\u25CA","\\Diamond"),i(l,d,g,"\xA5","\\yen",!0),i(M,d,g,"\xA5","\\yen",!0),i(l,d,g,"\u2713","\\checkmark",!0),i(M,d,g,"\u2713","\\checkmark"),i(l,d,g,"\u2136","\\beth",!0),i(l,d,g,"\u2138","\\daleth",!0),i(l,d,g,"\u2137","\\gimel",!0),i(l,d,g,"\u03DD","\\digamma",!0),i(l,d,g,"\u03F0","\\varkappa"),i(l,d,v0,"\u250C","\\@ulcorner",!0),i(l,d,b0,"\u2510","\\@urcorner",!0),i(l,d,v0,"\u2514","\\@llcorner",!0),i(l,d,b0,"\u2518","\\@lrcorner",!0),i(l,d,p,"\u2266","\\leqq",!0),i(l,d,p,"\u2A7D","\\leqslant",!0),i(l,d,p,"\u2A95","\\eqslantless",!0),i(l,d,p,"\u2272","\\lesssim",!0),i(l,d,p,"\u2A85","\\lessapprox",!0),i(l,d,p,"\u224A","\\approxeq",!0),i(l,d,C,"\u22D6","\\lessdot"),i(l,d,p,"\u22D8","\\lll",!0),i(l,d,p,"\u2276","\\lessgtr",!0),i(l,d,p,"\u22DA","\\lesseqgtr",!0),i(l,d,p,"\u2A8B","\\lesseqqgtr",!0),i(l,d,p,"\u2251","\\doteqdot"),i(l,d,p,"\u2253","\\risingdotseq",!0),i(l,d,p,"\u2252","\\fallingdotseq",!0),i(l,d,p,"\u223D","\\backsim",!0),i(l,d,p,"\u22CD","\\backsimeq",!0),i(l,d,p,"\u2AC5","\\subseteqq",!0),i(l,d,p,"\u22D0","\\Subset",!0),i(l,d,p,"\u228F","\\sqsubset",!0),i(l,d,p,"\u227C","\\preccurlyeq",!0),i(l,d,p,"\u22DE","\\curlyeqprec",!0),i(l,d,p,"\u227E","\\precsim",!0),i(l,d,p,"\u2AB7","\\precapprox",!0),i(l,d,p,"\u22B2","\\vartriangleleft"),i(l,d,p,"\u22B4","\\trianglelefteq"),i(l,d,p,"\u22A8","\\vDash",!0),i(l,d,p,"\u22AA","\\Vvdash",!0),i(l,d,p,"\u2323","\\smallsmile"),i(l,d,p,"\u2322","\\smallfrown"),i(l,d,p,"\u224F","\\bumpeq",!0),i(l,d,p,"\u224E","\\Bumpeq",!0),i(l,d,p,"\u2267","\\geqq",!0),i(l,d,p,"\u2A7E","\\geqslant",!0),i(l,d,p,"\u2A96","\\eqslantgtr",!0),i(l,d,p,"\u2273","\\gtrsim",!0),i(l,d,p,"\u2A86","\\gtrapprox",!0),i(l,d,C,"\u22D7","\\gtrdot"),i(l,d,p,"\u22D9","\\ggg",!0),i(l,d,p,"\u2277","\\gtrless",!0),i(l,d,p,"\u22DB","\\gtreqless",!0),i(l,d,p,"\u2A8C","\\gtreqqless",!0),i(l,d,p,"\u2256","\\eqcirc",!0),i(l,d,p,"\u2257","\\circeq",!0),i(l,d,p,"\u225C","\\triangleq",!0),i(l,d,p,"\u223C","\\thicksim"),i(l,d,p,"\u2248","\\thickapprox"),i(l,d,p,"\u2AC6","\\supseteqq",!0),i(l,d,p,"\u22D1","\\Supset",!0),i(l,d,p,"\u2290","\\sqsupset",!0),i(l,d,p,"\u227D","\\succcurlyeq",!0),i(l,d,p,"\u22DF","\\curlyeqsucc",!0),i(l,d,p,"\u227F","\\succsim",!0),i(l,d,p,"\u2AB8","\\succapprox",!0),i(l,d,p,"\u22B3","\\vartriangleright"),i(l,d,p,"\u22B5","\\trianglerighteq"),i(l,d,p,"\u22A9","\\Vdash",!0),i(l,d,p,"\u2223","\\shortmid"),i(l,d,p,"\u2225","\\shortparallel"),i(l,d,p,"\u226C","\\between",!0),i(l,d,p,"\u22D4","\\pitchfork",!0),i(l,d,p,"\u221D","\\varpropto"),i(l,d,p,"\u25C0","\\blacktriangleleft"),i(l,d,p,"\u2234","\\therefore",!0),i(l,d,p,"\u220D","\\backepsilon"),i(l,d,p,"\u25B6","\\blacktriangleright"),i(l,d,p,"\u2235","\\because",!0),i(l,d,p,"\u22D8","\\llless"),i(l,d,p,"\u22D9","\\gggtr"),i(l,d,C,"\u22B2","\\lhd"),i(l,d,C,"\u22B3","\\rhd"),i(l,d,p,"\u2242","\\eqsim",!0),i(l,c,p,"\u22C8","\\Join"),i(l,d,p,"\u2251","\\Doteq",!0),i(l,d,C,"\u2214","\\dotplus",!0),i(l,d,C,"\u2216","\\smallsetminus"),i(l,d,C,"\u22D2","\\Cap",!0),i(l,d,C,"\u22D3","\\Cup",!0),i(l,d,C,"\u2A5E","\\doublebarwedge",!0),i(l,d,C,"\u229F","\\boxminus",!0),i(l,d,C,"\u229E","\\boxplus",!0),i(l,d,C,"\u22C7","\\divideontimes",!0),i(l,d,C,"\u22C9","\\ltimes",!0),i(l,d,C,"\u22CA","\\rtimes",!0),i(l,d,C,"\u22CB","\\leftthreetimes",!0),i(l,d,C,"\u22CC","\\rightthreetimes",!0),i(l,d,C,"\u22CF","\\curlywedge",!0),i(l,d,C,"\u22CE","\\curlyvee",!0),i(l,d,C,"\u229D","\\circleddash",!0),i(l,d,C,"\u229B","\\circledast",!0),i(l,d,C,"\u22C5","\\centerdot"),i(l,d,C,"\u22BA","\\intercal",!0),i(l,d,C,"\u22D2","\\doublecap"),i(l,d,C,"\u22D3","\\doublecup"),i(l,d,C,"\u22A0","\\boxtimes",!0),i(l,d,p,"\u21E2","\\dashrightarrow",!0),i(l,d,p,"\u21E0","\\dashleftarrow",!0),i(l,d,p,"\u21C7","\\leftleftarrows",!0),i(l,d,p,"\u21C6","\\leftrightarrows",!0),i(l,d,p,"\u21DA","\\Lleftarrow",!0),i(l,d,p,"\u219E","\\twoheadleftarrow",!0),i(l,d,p,"\u21A2","\\leftarrowtail",!0),i(l,d,p,"\u21AB","\\looparrowleft",!0),i(l,d,p,"\u21CB","\\leftrightharpoons",!0),i(l,d,p,"\u21B6","\\curvearrowleft",!0),i(l,d,p,"\u21BA","\\circlearrowleft",!0),i(l,d,p,"\u21B0","\\Lsh",!0),i(l,d,p,"\u21C8","\\upuparrows",!0),i(l,d,p,"\u21BF","\\upharpoonleft",!0),i(l,d,p,"\u21C3","\\downharpoonleft",!0),i(l,c,p,"\u22B6","\\origof",!0),i(l,c,p,"\u22B7","\\imageof",!0),i(l,d,p,"\u22B8","\\multimap",!0),i(l,d,p,"\u21AD","\\leftrightsquigarrow",!0),i(l,d,p,"\u21C9","\\rightrightarrows",!0),i(l,d,p,"\u21C4","\\rightleftarrows",!0),i(l,d,p,"\u21A0","\\twoheadrightarrow",!0),i(l,d,p,"\u21A3","\\rightarrowtail",!0),i(l,d,p,"\u21AC","\\looparrowright",!0),i(l,d,p,"\u21B7","\\curvearrowright",!0),i(l,d,p,"\u21BB","\\circlearrowright",!0),i(l,d,p,"\u21B1","\\Rsh",!0),i(l,d,p,"\u21CA","\\downdownarrows",!0),i(l,d,p,"\u21BE","\\upharpoonright",!0),i(l,d,p,"\u21C2","\\downharpoonright",!0),i(l,d,p,"\u21DD","\\rightsquigarrow",!0),i(l,d,p,"\u21DD","\\leadsto"),i(l,d,p,"\u21DB","\\Rrightarrow",!0),i(l,d,p,"\u21BE","\\restriction"),i(l,c,g,"\u2018","`"),i(l,c,g,"$","\\$"),i(M,c,g,"$","\\$"),i(M,c,g,"$","\\textdollar"),i(l,c,g,"%","\\%"),i(M,c,g,"%","\\%"),i(l,c,g,"_","\\_"),i(M,c,g,"_","\\_"),i(M,c,g,"_","\\textunderscore"),i(l,c,g,"\u2220","\\angle",!0),i(l,c,g,"\u221E","\\infty",!0),i(l,c,g,"\u2032","\\prime"),i(l,c,g,"\u25B3","\\triangle"),i(l,c,g,"\u0393","\\Gamma",!0),i(l,c,g,"\u0394","\\Delta",!0),i(l,c,g,"\u0398","\\Theta",!0),i(l,c,g,"\u039B","\\Lambda",!0),i(l,c,g,"\u039E","\\Xi",!0),i(l,c,g,"\u03A0","\\Pi",!0),i(l,c,g,"\u03A3","\\Sigma",!0),i(l,c,g,"\u03A5","\\Upsilon",!0),i(l,c,g,"\u03A6","\\Phi",!0),i(l,c,g,"\u03A8","\\Psi",!0),i(l,c,g,"\u03A9","\\Omega",!0),i(l,c,g,"A","\u0391"),i(l,c,g,"B","\u0392"),i(l,c,g,"E","\u0395"),i(l,c,g,"Z","\u0396"),i(l,c,g,"H","\u0397"),i(l,c,g,"I","\u0399"),i(l,c,g,"K","\u039A"),i(l,c,g,"M","\u039C"),i(l,c,g,"N","\u039D"),i(l,c,g,"O","\u039F"),i(l,c,g,"P","\u03A1"),i(l,c,g,"T","\u03A4"),i(l,c,g,"X","\u03A7"),i(l,c,g,"\xAC","\\neg",!0),i(l,c,g,"\xAC","\\lnot"),i(l,c,g,"\u22A4","\\top"),i(l,c,g,"\u22A5","\\bot"),i(l,c,g,"\u2205","\\emptyset"),i(l,d,g,"\u2205","\\varnothing"),i(l,c,I,"\u03B1","\\alpha",!0),i(l,c,I,"\u03B2","\\beta",!0),i(l,c,I,"\u03B3","\\gamma",!0),i(l,c,I,"\u03B4","\\delta",!0),i(l,c,I,"\u03F5","\\epsilon",!0),i(l,c,I,"\u03B6","\\zeta",!0),i(l,c,I,"\u03B7","\\eta",!0),i(l,c,I,"\u03B8","\\theta",!0),i(l,c,I,"\u03B9","\\iota",!0),i(l,c,I,"\u03BA","\\kappa",!0),i(l,c,I,"\u03BB","\\lambda",!0),i(l,c,I,"\u03BC","\\mu",!0),i(l,c,I,"\u03BD","\\nu",!0),i(l,c,I,"\u03BE","\\xi",!0),i(l,c,I,"\u03BF","\\omicron",!0),i(l,c,I,"\u03C0","\\pi",!0),i(l,c,I,"\u03C1","\\rho",!0),i(l,c,I,"\u03C3","\\sigma",!0),i(l,c,I,"\u03C4","\\tau",!0),i(l,c,I,"\u03C5","\\upsilon",!0),i(l,c,I,"\u03D5","\\phi",!0),i(l,c,I,"\u03C7","\\chi",!0),i(l,c,I,"\u03C8","\\psi",!0),i(l,c,I,"\u03C9","\\omega",!0),i(l,c,I,"\u03B5","\\varepsilon",!0),i(l,c,I,"\u03D1","\\vartheta",!0),i(l,c,I,"\u03D6","\\varpi",!0),i(l,c,I,"\u03F1","\\varrho",!0),i(l,c,I,"\u03C2","\\varsigma",!0),i(l,c,I,"\u03C6","\\varphi",!0),i(l,c,C,"\u2217","*",!0),i(l,c,C,"+","+"),i(l,c,C,"\u2212","-",!0),i(l,c,C,"\u22C5","\\cdot",!0),i(l,c,C,"\u2218","\\circ",!0),i(l,c,C,"\xF7","\\div",!0),i(l,c,C,"\xB1","\\pm",!0),i(l,c,C,"\xD7","\\times",!0),i(l,c,C,"\u2229","\\cap",!0),i(l,c,C,"\u222A","\\cup",!0),i(l,c,C,"\u2216","\\setminus",!0),i(l,c,C,"\u2227","\\land"),i(l,c,C,"\u2228","\\lor"),i(l,c,C,"\u2227","\\wedge",!0),i(l,c,C,"\u2228","\\vee",!0),i(l,c,g,"\u221A","\\surd"),i(l,c,v0,"\u27E8","\\langle",!0),i(l,c,v0,"\u2223","\\lvert"),i(l,c,v0,"\u2225","\\lVert"),i(l,c,b0,"?","?"),i(l,c,b0,"!","!"),i(l,c,b0,"\u27E9","\\rangle",!0),i(l,c,b0,"\u2223","\\rvert"),i(l,c,b0,"\u2225","\\rVert"),i(l,c,p,"=","="),i(l,c,p,":",":"),i(l,c,p,"\u2248","\\approx",!0),i(l,c,p,"\u2245","\\cong",!0),i(l,c,p,"\u2265","\\ge"),i(l,c,p,"\u2265","\\geq",!0),i(l,c,p,"\u2190","\\gets"),i(l,c,p,">","\\gt",!0),i(l,c,p,"\u2208","\\in",!0),i(l,c,p,"\uE020","\\@not"),i(l,c,p,"\u2282","\\subset",!0),i(l,c,p,"\u2283","\\supset",!0),i(l,c,p,"\u2286","\\subseteq",!0),i(l,c,p,"\u2287","\\supseteq",!0),i(l,d,p,"\u2288","\\nsubseteq",!0),i(l,d,p,"\u2289","\\nsupseteq",!0),i(l,c,p,"\u22A8","\\models"),i(l,c,p,"\u2190","\\leftarrow",!0),i(l,c,p,"\u2264","\\le"),i(l,c,p,"\u2264","\\leq",!0),i(l,c,p,"<","\\lt",!0),i(l,c,p,"\u2192","\\rightarrow",!0),i(l,c,p,"\u2192","\\to"),i(l,d,p,"\u2271","\\ngeq",!0),i(l,d,p,"\u2270","\\nleq",!0),i(l,c,V0,"\xA0","\\ "),i(l,c,V0,"\xA0","\\space"),i(l,c,V0,"\xA0","\\nobreakspace"),i(M,c,V0,"\xA0","\\ "),i(M,c,V0,"\xA0"," "),i(M,c,V0,"\xA0","\\space"),i(M,c,V0,"\xA0","\\nobreakspace"),i(l,c,V0,null,"\\nobreak"),i(l,c,V0,null,"\\allowbreak"),i(l,c,ve,",",","),i(l,c,ve,";",";"),i(l,d,C,"\u22BC","\\barwedge",!0),i(l,d,C,"\u22BB","\\veebar",!0),i(l,c,C,"\u2299","\\odot",!0),i(l,c,C,"\u2295","\\oplus",!0),i(l,c,C,"\u2297","\\otimes",!0),i(l,c,g,"\u2202","\\partial",!0),i(l,c,C,"\u2298","\\oslash",!0),i(l,d,C,"\u229A","\\circledcirc",!0),i(l,d,C,"\u22A1","\\boxdot",!0),i(l,c,C,"\u25B3","\\bigtriangleup"),i(l,c,C,"\u25BD","\\bigtriangledown"),i(l,c,C,"\u2020","\\dagger"),i(l,c,C,"\u22C4","\\diamond"),i(l,c,C,"\u22C6","\\star"),i(l,c,C,"\u25C3","\\triangleleft"),i(l,c,C,"\u25B9","\\triangleright"),i(l,c,v0,"{","\\{"),i(M,c,g,"{","\\{"),i(M,c,g,"{","\\textbraceleft"),i(l,c,b0,"}","\\}"),i(M,c,g,"}","\\}"),i(M,c,g,"}","\\textbraceright"),i(l,c,v0,"{","\\lbrace"),i(l,c,b0,"}","\\rbrace"),i(l,c,v0,"[","\\lbrack",!0),i(M,c,g,"[","\\lbrack",!0),i(l,c,b0,"]","\\rbrack",!0),i(M,c,g,"]","\\rbrack",!0),i(l,c,v0,"(","\\lparen",!0),i(l,c,b0,")","\\rparen",!0),i(M,c,g,"<","\\textless",!0),i(M,c,g,">","\\textgreater",!0),i(l,c,v0,"\u230A","\\lfloor",!0),i(l,c,b0,"\u230B","\\rfloor",!0),i(l,c,v0,"\u2308","\\lceil",!0),i(l,c,b0,"\u2309","\\rceil",!0),i(l,c,g,"\\","\\backslash"),i(l,c,g,"\u2223","|"),i(l,c,g,"\u2223","\\vert"),i(M,c,g,"|","\\textbar",!0),i(l,c,g,"\u2225","\\|"),i(l,c,g,"\u2225","\\Vert"),i(M,c,g,"\u2225","\\textbardbl"),i(M,c,g,"~","\\textasciitilde"),i(M,c,g,"\\","\\textbackslash"),i(M,c,g,"^","\\textasciicircum"),i(l,c,p,"\u2191","\\uparrow",!0),i(l,c,p,"\u21D1","\\Uparrow",!0),i(l,c,p,"\u2193","\\downarrow",!0),i(l,c,p,"\u21D3","\\Downarrow",!0),i(l,c,p,"\u2195","\\updownarrow",!0),i(l,c,p,"\u21D5","\\Updownarrow",!0),i(l,c,m0,"\u2210","\\coprod"),i(l,c,m0,"\u22C1","\\bigvee"),i(l,c,m0,"\u22C0","\\bigwedge"),i(l,c,m0,"\u2A04","\\biguplus"),i(l,c,m0,"\u22C2","\\bigcap"),i(l,c,m0,"\u22C3","\\bigcup"),i(l,c,m0,"\u222B","\\int"),i(l,c,m0,"\u222B","\\intop"),i(l,c,m0,"\u222C","\\iint"),i(l,c,m0,"\u222D","\\iiint"),i(l,c,m0,"\u220F","\\prod"),i(l,c,m0,"\u2211","\\sum"),i(l,c,m0,"\u2A02","\\bigotimes"),i(l,c,m0,"\u2A01","\\bigoplus"),i(l,c,m0,"\u2A00","\\bigodot"),i(l,c,m0,"\u222E","\\oint"),i(l,c,m0,"\u222F","\\oiint"),i(l,c,m0,"\u2230","\\oiiint"),i(l,c,m0,"\u2A06","\\bigsqcup"),i(l,c,m0,"\u222B","\\smallint"),i(M,c,se,"\u2026","\\textellipsis"),i(l,c,se,"\u2026","\\mathellipsis"),i(M,c,se,"\u2026","\\ldots",!0),i(l,c,se,"\u2026","\\ldots",!0),i(l,c,se,"\u22EF","\\@cdots",!0),i(l,c,se,"\u22F1","\\ddots",!0),i(l,c,g,"\u22EE","\\varvdots"),i(M,c,g,"\u22EE","\\varvdots"),i(l,c,e0,"\u02CA","\\acute"),i(l,c,e0,"\u02CB","\\grave"),i(l,c,e0,"\xA8","\\ddot"),i(l,c,e0,"~","\\tilde"),i(l,c,e0,"\u02C9","\\bar"),i(l,c,e0,"\u02D8","\\breve"),i(l,c,e0,"\u02C7","\\check"),i(l,c,e0,"^","\\hat"),i(l,c,e0,"\u20D7","\\vec"),i(l,c,e0,"\u02D9","\\dot"),i(l,c,e0,"\u02DA","\\mathring"),i(l,c,I,"\uE131","\\@imath"),i(l,c,I,"\uE237","\\@jmath"),i(l,c,g,"\u0131","\u0131"),i(l,c,g,"\u0237","\u0237"),i(M,c,g,"\u0131","\\i",!0),i(M,c,g,"\u0237","\\j",!0),i(M,c,g,"\xDF","\\ss",!0),i(M,c,g,"\xE6","\\ae",!0),i(M,c,g,"\u0153","\\oe",!0),i(M,c,g,"\xF8","\\o",!0),i(M,c,g,"\xC6","\\AE",!0),i(M,c,g,"\u0152","\\OE",!0),i(M,c,g,"\xD8","\\O",!0),i(M,c,e0,"\u02CA","\\'"),i(M,c,e0,"\u02CB","\\`"),i(M,c,e0,"\u02C6","\\^"),i(M,c,e0,"\u02DC","\\~"),i(M,c,e0,"\u02C9","\\="),i(M,c,e0,"\u02D8","\\u"),i(M,c,e0,"\u02D9","\\."),i(M,c,e0,"\xB8","\\c"),i(M,c,e0,"\u02DA","\\r"),i(M,c,e0,"\u02C7","\\v"),i(M,c,e0,"\xA8",'\\"'),i(M,c,e0,"\u02DD","\\H"),i(M,c,e0,"\u25EF","\\textcircled");let Lt={"--":!0,"---":!0,"``":!0,"''":!0};i(M,c,g,"\u2013","--",!0),i(M,c,g,"\u2013","\\textendash"),i(M,c,g,"\u2014","---",!0),i(M,c,g,"\u2014","\\textemdash"),i(M,c,g,"\u2018","`",!0),i(M,c,g,"\u2018","\\textquoteleft"),i(M,c,g,"\u2019","'",!0),i(M,c,g,"\u2019","\\textquoteright"),i(M,c,g,"\u201C","``",!0),i(M,c,g,"\u201C","\\textquotedblleft"),i(M,c,g,"\u201D","''",!0),i(M,c,g,"\u201D","\\textquotedblright"),i(l,c,g,"\xB0","\\degree",!0),i(M,c,g,"\xB0","\\degree"),i(M,c,g,"\xB0","\\textdegree",!0),i(l,c,g,"\xA3","\\pounds"),i(l,c,g,"\xA3","\\mathsterling",!0),i(M,c,g,"\xA3","\\pounds"),i(M,c,g,"\xA3","\\textsterling",!0),i(l,d,g,"\u2720","\\maltese"),i(M,d,g,"\u2720","\\maltese");let Ft='0123456789/@."';for(let t=0;t<Ft.length;t++){let e=Ft.charAt(t);i(l,c,g,e,e)}let _t='0123456789!@*()-=+";:?/.,';for(let t=0;t<_t.length;t++){let e=_t.charAt(t);i(M,c,g,e,e)}let Se="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";for(let t=0;t<Se.length;t++){let e=Se.charAt(t);i(l,c,I,e,e),i(M,c,g,e,e)}i(l,d,g,"C","\u2102"),i(M,d,g,"C","\u2102"),i(l,d,g,"H","\u210D"),i(M,d,g,"H","\u210D"),i(l,d,g,"N","\u2115"),i(M,d,g,"N","\u2115"),i(l,d,g,"P","\u2119"),i(M,d,g,"P","\u2119"),i(l,d,g,"Q","\u211A"),i(M,d,g,"Q","\u211A"),i(l,d,g,"R","\u211D"),i(M,d,g,"R","\u211D"),i(l,d,g,"Z","\u2124"),i(M,d,g,"Z","\u2124"),i(l,c,I,"h","\u210E"),i(M,c,I,"h","\u210E");let O="";for(let t=0;t<Se.length;t++){let e=Se.charAt(t);O=String.fromCharCode(55349,56320+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,56372+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,56424+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,56580+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,56684+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,56736+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,56788+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,56840+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,56944+t),i(l,c,I,e,O),i(M,c,g,e,O),t<26&&(O=String.fromCharCode(55349,56632+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,56476+t),i(l,c,I,e,O),i(M,c,g,e,O))}O="\u{1D55C}",i(l,c,I,"k",O),i(M,c,g,"k",O);for(let t=0;t<10;t++){let e=t.toString();O=String.fromCharCode(55349,57294+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,57314+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,57324+t),i(l,c,I,e,O),i(M,c,g,e,O),O=String.fromCharCode(55349,57334+t),i(l,c,I,e,O),i(M,c,g,e,O)}let Ye="\xD0\xDE\xFE";for(let t=0;t<Ye.length;t++){let e=Ye.charAt(t);i(l,c,I,e,e),i(M,c,g,e,e)}let Me=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],Pt=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],Fn=function(t,e){let r=t.charCodeAt(0),n=t.charCodeAt(1),s=(r-55296)*1024+(n-56320)+65536,a=e==="math"?0:1;if(119808<=s&&s<120484){let o=Math.floor((s-119808)/26);return[Me[o][2],Me[o][a]]}else if(120782<=s&&s<=120831){let o=Math.floor((s-120782)/10);return[Pt[o][2],Pt[o][a]]}else{if(s===120485||s===120486)return[Me[0][2],Me[0][a]];if(120486<s&&s<120782)return["",""];throw new v("Unsupported character: "+t)}},ze=function(t,e,r){return s0[r][t]&&s0[r][t].replace&&(t=s0[r][t].replace),{value:t,metrics:Ve(t,e,r)}},D0=function(t,e,r,n,s){let a=ze(t,e,r),o=a.metrics;t=a.value;let u;if(o){let m=o.italic;(r==="text"||n&&n.font==="mathit")&&(m=0),u=new A0(t,o.height,o.depth,m,o.skew,o.width,s)}else typeof console<"u"&&console.warn("No character metrics "+("for '"+t+"' in style '"+e+"' and mode '"+r+"'")),u=new A0(t,0,0,0,0,0,s);if(n){u.maxFontSize=n.sizeMultiplier,n.style.isTight()&&u.classes.push("mtight");let m=n.getColor();m&&(u.style.color=m)}return u},_n=function(t,e,r,n){return n===void 0&&(n=[]),r.font==="boldsymbol"&&ze(t,"Main-Bold",e).metrics?D0(t,"Main-Bold",e,r,n.concat(["mathbf"])):t==="\\"||s0[e][t].font==="main"?D0(t,"Main-Regular",e,r,n):D0(t,"AMS-Regular",e,r,n.concat(["amsrm"]))},Pn=function(t,e,r,n,s){return s!=="textord"&&ze(t,"Math-BoldItalic",e).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}},Vn=function(t,e,r){let n=t.mode,s=t.text,a=["mord"],o=n==="math"||n==="text"&&e.font,u=o?e.font:e.fontFamily,m="",f="";if(s.charCodeAt(0)===55349&&([m,f]=Fn(s,n)),m.length>0)return D0(s,m,n,e,a.concat(f));if(u){let b,y;if(u==="boldsymbol"){let w=Pn(s,n,e,a,r);b=w.fontName,y=[w.fontClass]}else o?(b=$t[u].fontName,y=[u]):(b=Ae(u,e.fontWeight,e.fontShape),y=[u,e.fontWeight,e.fontShape]);if(ze(s,b,n).metrics)return D0(s,b,n,e,a.concat(y));if(Lt.hasOwnProperty(s)&&b.slice(0,10)==="Typewriter"){let w=[];for(let S=0;S<s.length;S++)w.push(D0(s[S],b,n,e,a.concat(y)));return Gt(w)}}if(r==="mathord")return D0(s,"Math-Italic",n,e,a.concat(["mathnormal"]));if(r==="textord"){let b=s0[n][s]&&s0[n][s].font;if(b==="ams"){let y=Ae("amsrm",e.fontWeight,e.fontShape);return D0(s,y,n,e,a.concat("amsrm",e.fontWeight,e.fontShape))}else if(b==="main"||!b){let y=Ae("textrm",e.fontWeight,e.fontShape);return D0(s,y,n,e,a.concat(e.fontWeight,e.fontShape))}else{let y=Ae(b,e.fontWeight,e.fontShape);return D0(s,y,n,e,a.concat(y,e.fontWeight,e.fontShape))}}else throw new Error("unexpected type: "+r+" in makeOrd")},Gn=(t,e)=>{if(Y0(t.classes)!==Y0(e.classes)||t.skew!==e.skew||t.maxFontSize!==e.maxFontSize)return!1;if(t.classes.length===1){let r=t.classes[0];if(r==="mbin"||r==="mord")return!1}for(let r in t.style)if(t.style.hasOwnProperty(r)&&t.style[r]!==e.style[r])return!1;for(let r in e.style)if(e.style.hasOwnProperty(r)&&t.style[r]!==e.style[r])return!1;return!0},$n=t=>{for(let e=0;e<t.length-1;e++){let r=t[e],n=t[e+1];r instanceof A0&&n instanceof A0&&Gn(r,n)&&(r.text+=n.text,r.height=Math.max(r.height,n.height),r.depth=Math.max(r.depth,n.depth),r.italic=n.italic,t.splice(e+1,1),e--)}return t},Xe=function(t){let e=0,r=0,n=0;for(let s=0;s<t.children.length;s++){let a=t.children[s];a.height>e&&(e=a.height),a.depth>r&&(r=a.depth),a.maxFontSize>n&&(n=a.maxFontSize)}t.height=e,t.depth=r,t.maxFontSize=n},x0=function(t,e,r,n){let s=new he(t,e,r,n);return Xe(s),s},Vt=(t,e,r,n)=>new he(t,e,r,n),Un=function(t,e,r){let n=x0([t],[],e);return n.height=Math.max(r||e.fontMetrics().defaultRuleThickness,e.minRuleThickness),n.style.borderBottomWidth=A(n.height),n.maxFontSize=1,n},Wn=function(t,e,r,n){let s=new Ue(t,e,r,n);return Xe(s),s},Gt=function(t){let e=new ue(t);return Xe(e),e},Yn=function(t,e){return t instanceof ue?x0([],[t],e):t},Xn=function(t){if(t.positionType==="individualShift"){let r=t.children,n=[r[0]],s=-r[0].shift-r[0].elem.depth,a=s;for(let o=1;o<r.length;o++){let u=-r[o].shift-a-r[o].elem.depth,m=u-(r[o-1].elem.height+r[o-1].elem.depth);a=a+u,n.push({type:"kern",size:m}),n.push(r[o])}return{children:n,depth:s}}let e;if(t.positionType==="top"){let r=t.positionData;for(let n=0;n<t.children.length;n++){let s=t.children[n];r-=s.type==="kern"?s.size:s.elem.height+s.elem.depth}e=r}else if(t.positionType==="bottom")e=-t.positionData;else{let r=t.children[0];if(r.type!=="elem")throw new Error('First child must have type "elem".');if(t.positionType==="shift")e=-r.elem.depth-t.positionData;else if(t.positionType==="firstBaseline")e=-r.elem.depth;else throw new Error("Invalid positionType "+t.positionType+".")}return{children:t.children,depth:e}},jn=function(t,e){let{children:r,depth:n}=Xn(t),s=0;for(let S=0;S<r.length;S++){let D=r[S];if(D.type==="elem"){let q=D.elem;s=Math.max(s,q.maxFontSize,q.height)}}s+=2;let a=x0(["pstrut"],[]);a.style.height=A(s);let o=[],u=n,m=n,f=n;for(let S=0;S<r.length;S++){let D=r[S];if(D.type==="kern")f+=D.size;else{let q=D.elem,V=D.wrapperClasses||[],_=D.wrapperStyle||{},G=x0(V,[a,q],void 0,_);G.style.top=A(-s-f-q.depth),D.marginLeft&&(G.style.marginLeft=D.marginLeft),D.marginRight&&(G.style.marginRight=D.marginRight),o.push(G),f+=q.height+q.depth}u=Math.min(u,f),m=Math.max(m,f)}let b=x0(["vlist"],o);b.style.height=A(m);let y;if(u<0){let S=x0([],[]),D=x0(["vlist"],[S]);D.style.height=A(-u);let q=x0(["vlist-s"],[new A0("\u200B")]);y=[x0(["vlist-r"],[b,q]),x0(["vlist-r"],[D])]}else y=[x0(["vlist-r"],[b])];let w=x0(["vlist-t"],y);return y.length===2&&w.classes.push("vlist-t2"),w.height=m,w.depth=-u,w},Zn=(t,e)=>{let r=x0(["mspace"],[],e),n=n0(t,e);return r.style.marginRight=A(n),r},Ae=function(t,e,r){let n="";switch(t){case"amsrm":n="AMS";break;case"textrm":n="Main";break;case"textsf":n="SansSerif";break;case"texttt":n="Typewriter";break;default:n=t}let s;return e==="textbf"&&r==="textit"?s="BoldItalic":e==="textbf"?s="Bold":e==="textit"?s="Italic":s="Regular",n+"-"+s},$t={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathsfit:{variant:"sans-serif-italic",fontName:"SansSerif-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},Ut={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]};var x={fontMap:$t,makeSymbol:D0,mathsym:_n,makeSpan:x0,makeSvgSpan:Vt,makeLineSpan:Un,makeAnchor:Wn,makeFragment:Gt,wrapFragment:Yn,makeVList:jn,makeOrd:Vn,makeGlue:Zn,staticSvg:function(t,e){let[r,n,s]=Ut[t],a=new X0(r),o=new P0([a],{width:A(n),height:A(s),style:"width:"+A(n),viewBox:"0 0 "+1e3*n+" "+1e3*s,preserveAspectRatio:"xMinYMin"}),u=Vt(["overlay"],[o],e);return u.height=s,u.style.height=A(s),u.style.width=A(n),u},svgData:Ut,tryCombineChars:$n};let i0={number:3,unit:"mu"},J0={number:4,unit:"mu"},G0={number:5,unit:"mu"},Kn={mord:{mop:i0,mbin:J0,mrel:G0,minner:i0},mop:{mord:i0,mop:i0,mrel:G0,minner:i0},mbin:{mord:J0,mop:J0,mopen:J0,minner:J0},mrel:{mord:G0,mop:G0,mopen:G0,minner:G0},mopen:{},mclose:{mop:i0,mbin:J0,mrel:G0,minner:i0},mpunct:{mord:i0,mop:i0,mrel:G0,mopen:i0,mclose:i0,mpunct:i0,minner:i0},minner:{mord:i0,mop:i0,mbin:J0,mrel:G0,mopen:i0,mpunct:i0,minner:i0}},Jn={mord:{mop:i0},mop:{mord:i0,mop:i0},mbin:{},mrel:{},mopen:{},mclose:{mop:i0},mpunct:{},minner:{mop:i0}},Wt={},Te={},Be={};function B(t){let{type:e,names:r,props:n,handler:s,htmlBuilder:a,mathmlBuilder:o}=t,u={type:e,numArgs:n.numArgs,argTypes:n.argTypes,allowedInArgument:!!n.allowedInArgument,allowedInText:!!n.allowedInText,allowedInMath:n.allowedInMath===void 0?!0:n.allowedInMath,numOptionalArgs:n.numOptionalArgs||0,infix:!!n.infix,primitive:!!n.primitive,handler:s};for(let m=0;m<r.length;++m)Wt[r[m]]=u;e&&(a&&(Te[e]=a),o&&(Be[e]=o))}function Q0(t){let{type:e,htmlBuilder:r,mathmlBuilder:n}=t;B({type:e,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:r,mathmlBuilder:n})}let Ne=function(t){return t.type==="ordgroup"&&t.body.length===1?t.body[0]:t},u0=function(t){return t.type==="ordgroup"?t.body:[t]},$0=x.makeSpan,Qn=["leftmost","mbin","mopen","mrel","mop","mpunct"],e1=["rightmost","mrel","mclose","mpunct"],t1={display:E.DISPLAY,text:E.TEXT,script:E.SCRIPT,scriptscript:E.SCRIPTSCRIPT},r1={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},d0=function(t,e,r,n){n===void 0&&(n=[null,null]);let s=[];for(let f=0;f<t.length;f++){let b=U(t[f],e);if(b instanceof ue){let y=b.children;s.push(...y)}else s.push(b)}if(x.tryCombineChars(s),!r)return s;let a=e;if(t.length===1){let f=t[0];f.type==="sizing"?a=e.havingSize(f.size):f.type==="styling"&&(a=e.havingStyle(t1[f.style]))}let o=$0([n[0]||"leftmost"],[],e),u=$0([n[1]||"rightmost"],[],e),m=r==="root";return je(s,(f,b)=>{let y=b.classes[0],w=f.classes[0];y==="mbin"&&T.contains(e1,w)?b.classes[0]="mord":w==="mbin"&&T.contains(Qn,y)&&(f.classes[0]="mord")},{node:o},u,m),je(s,(f,b)=>{let y=Ke(b),w=Ke(f),S=y&&w?f.hasClass("mtight")?Jn[y][w]:Kn[y][w]:null;if(S)return x.makeGlue(S,a)},{node:o},u,m),s},je=function(t,e,r,n,s){n&&t.push(n);let a=0;for(;a<t.length;a++){let o=t[a],u=Yt(o);if(u){je(u.children,e,r,null,s);continue}let m=!o.hasClass("mspace");if(m){let f=e(o,r.node);f&&(r.insertAfter?r.insertAfter(f):(t.unshift(f),a++))}m?r.node=o:s&&o.hasClass("newline")&&(r.node=$0(["leftmost"])),r.insertAfter=(f=>b=>{t.splice(f+1,0,b),a++})(a)}n&&t.pop()},Yt=function(t){return t instanceof ue||t instanceof Ue||t instanceof he&&t.hasClass("enclosing")?t:null},Ze=function(t,e){let r=Yt(t);if(r){let n=r.children;if(n.length){if(e==="right")return Ze(n[n.length-1],"right");if(e==="left")return Ze(n[0],"left")}}return t},Ke=function(t,e){return t?(e&&(t=Ze(t,e)),r1[t.classes[0]]||null):null},me=function(t,e){let r=["nulldelimiter"].concat(t.baseSizingClasses());return $0(e.concat(r))},U=function(t,e,r){if(!t)return $0();if(Te[t.type]){let n=Te[t.type](t,e);if(r&&e.size!==r.size){n=$0(e.sizingClasses(r),[n],e);let s=e.sizeMultiplier/r.sizeMultiplier;n.height*=s,n.depth*=s}return n}else throw new v("Got group of unknown type: '"+t.type+"'")};function Ce(t,e){let r=$0(["base"],t,e),n=$0(["strut"]);return n.style.height=A(r.height+r.depth),r.depth&&(n.style.verticalAlign=A(-r.depth)),r.children.unshift(n),r}function Je(t,e){let r=null;t.length===1&&t[0].type==="tag"&&(r=t[0].tag,t=t[0].body);let n=d0(t,e,"root"),s;n.length===2&&n[1].hasClass("tag")&&(s=n.pop());let a=[],o=[];for(let f=0;f<n.length;f++)if(o.push(n[f]),n[f].hasClass("mbin")||n[f].hasClass("mrel")||n[f].hasClass("allowbreak")){let b=!1;for(;f<n.length-1&&n[f+1].hasClass("mspace")&&!n[f+1].hasClass("newline");)f++,o.push(n[f]),n[f].hasClass("nobreak")&&(b=!0);b||(a.push(Ce(o,e)),o=[])}else n[f].hasClass("newline")&&(o.pop(),o.length>0&&(a.push(Ce(o,e)),o=[]),a.push(n[f]));o.length>0&&a.push(Ce(o,e));let u;r?(u=Ce(d0(r,e,!0)),u.classes=["tag"],a.push(u)):s&&a.push(s);let m=$0(["katex-html"],a);if(m.setAttribute("aria-hidden","true"),u){let f=u.children[0];f.style.height=A(m.height+m.depth),m.depth&&(f.style.verticalAlign=A(-m.depth))}return m}function Xt(t){return new ue(t)}class S0{constructor(e,r,n){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=r||[],this.classes=n||[]}setAttribute(e,r){this.attributes[e]=r}getAttribute(e){return this.attributes[e]}toNode(){let e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(let r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&e.setAttribute(r,this.attributes[r]);this.classes.length>0&&(e.className=Y0(this.classes));for(let r=0;r<this.children.length;r++)if(this.children[r]instanceof R0&&this.children[r+1]instanceof R0){let n=this.children[r].toText()+this.children[++r].toText();for(;this.children[r+1]instanceof R0;)n+=this.children[++r].toText();e.appendChild(new R0(n).toNode())}else e.appendChild(this.children[r].toNode());return e}toMarkup(){let e="<"+this.type;for(let r in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,r)&&(e+=" "+r+'="',e+=T.escape(this.attributes[r]),e+='"');this.classes.length>0&&(e+=' class ="'+T.escape(Y0(this.classes))+'"'),e+=">";for(let r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</"+this.type+">",e}toText(){return this.children.map(e=>e.toText()).join("")}}class R0{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return T.escape(this.toText())}toText(){return this.text}}class n1{constructor(e){this.width=void 0,this.character=void 0,this.width=e,e>=.05555&&e<=.05556?this.character="\u200A":e>=.1666&&e<=.1667?this.character="\u2009":e>=.2222&&e<=.2223?this.character="\u2005":e>=.2777&&e<=.2778?this.character="\u2005\u200A":e>=-.05556&&e<=-.05555?this.character="\u200A\u2063":e>=-.1667&&e<=-.1666?this.character="\u2009\u2063":e>=-.2223&&e<=-.2222?this.character="\u205F\u2063":e>=-.2778&&e<=-.2777?this.character="\u2005\u2063":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);{let e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",A(this.width)),e}}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+A(this.width)+'"/>'}toText(){return this.character?this.character:" "}}var z={MathNode:S0,TextNode:R0,SpaceNode:n1,newDocumentFragment:Xt};let T0=function(t,e,r){return s0[e][t]&&s0[e][t].replace&&t.charCodeAt(0)!==55349&&!(Lt.hasOwnProperty(t)&&r&&(r.fontFamily&&r.fontFamily.slice(4,6)==="tt"||r.font&&r.font.slice(4,6)==="tt"))&&(t=s0[e][t].replace),new z.TextNode(t)},Qe=function(t){return t.length===1?t[0]:new z.MathNode("mrow",t)},et=function(t,e){if(e.fontFamily==="texttt")return"monospace";if(e.fontFamily==="textsf")return e.fontShape==="textit"&&e.fontWeight==="textbf"?"sans-serif-bold-italic":e.fontShape==="textit"?"sans-serif-italic":e.fontWeight==="textbf"?"bold-sans-serif":"sans-serif";if(e.fontShape==="textit"&&e.fontWeight==="textbf")return"bold-italic";if(e.fontShape==="textit")return"italic";if(e.fontWeight==="textbf")return"bold";let r=e.font;if(!r||r==="mathnormal")return null;let n=t.mode;if(r==="mathit")return"italic";if(r==="boldsymbol")return t.type==="textord"?"bold":"bold-italic";if(r==="mathbf")return"bold";if(r==="mathbb")return"double-struck";if(r==="mathsfit")return"sans-serif-italic";if(r==="mathfrak")return"fraktur";if(r==="mathscr"||r==="mathcal")return"script";if(r==="mathsf")return"sans-serif";if(r==="mathtt")return"monospace";let s=t.text;if(T.contains(["\\imath","\\jmath"],s))return null;s0[n][s]&&s0[n][s].replace&&(s=s0[n][s].replace);let a=x.fontMap[r].fontName;return Ve(s,a,n)?x.fontMap[r].variant:null};function tt(t){if(!t)return!1;if(t.type==="mi"&&t.children.length===1){let e=t.children[0];return e instanceof R0&&e.text==="."}else if(t.type==="mo"&&t.children.length===1&&t.getAttribute("separator")==="true"&&t.getAttribute("lspace")==="0em"&&t.getAttribute("rspace")==="0em"){let e=t.children[0];return e instanceof R0&&e.text===","}else return!1}let w0=function(t,e,r){if(t.length===1){let a=Z(t[0],e);return r&&a instanceof S0&&a.type==="mo"&&(a.setAttribute("lspace","0em"),a.setAttribute("rspace","0em")),[a]}let n=[],s;for(let a=0;a<t.length;a++){let o=Z(t[a],e);if(o instanceof S0&&s instanceof S0){if(o.type==="mtext"&&s.type==="mtext"&&o.getAttribute("mathvariant")===s.getAttribute("mathvariant")){s.children.push(...o.children);continue}else if(o.type==="mn"&&s.type==="mn"){s.children.push(...o.children);continue}else if(tt(o)&&s.type==="mn"){s.children.push(...o.children);continue}else if(o.type==="mn"&&tt(s))o.children=[...s.children,...o.children],n.pop();else if((o.type==="msup"||o.type==="msub")&&o.children.length>=1&&(s.type==="mn"||tt(s))){let u=o.children[0];u instanceof S0&&u.type==="mn"&&(u.children=[...s.children,...u.children],n.pop())}else if(s.type==="mi"&&s.children.length===1){let u=s.children[0];if(u instanceof R0&&u.text==="\u0338"&&(o.type==="mo"||o.type==="mi"||o.type==="mn")){let m=o.children[0];m instanceof R0&&m.text.length>0&&(m.text=m.text.slice(0,1)+"\u0338"+m.text.slice(1),n.pop())}}}n.push(o),s=o}return n},j0=function(t,e,r){return Qe(w0(t,e,r))},Z=function(t,e){if(!t)return new z.MathNode("mrow");if(Be[t.type])return Be[t.type](t,e);throw new v("Got group of unknown type: '"+t.type+"'")};function jt(t,e,r,n,s){let a=w0(t,r),o;a.length===1&&a[0]instanceof S0&&T.contains(["mrow","mtable"],a[0].type)?o=a[0]:o=new z.MathNode("mrow",a);let u=new z.MathNode("annotation",[new z.TextNode(e)]);u.setAttribute("encoding","application/x-tex");let m=new z.MathNode("semantics",[o,u]),f=new z.MathNode("math",[m]);f.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),n&&f.setAttribute("display","block");let b=s?"katex":"katex-mathml";return x.makeSpan([b],[f])}let Zt=function(t){return new Dn({style:t.displayMode?E.DISPLAY:E.TEXT,maxSize:t.maxSize,minRuleThickness:t.minRuleThickness})},Kt=function(t,e){if(e.displayMode){let r=["katex-display"];e.leqno&&r.push("leqno"),e.fleqn&&r.push("fleqn"),t=x.makeSpan(r,[t])}return t},s1=function(t,e,r){let n=Zt(r),s;if(r.output==="mathml")return jt(t,e,n,r.displayMode,!0);if(r.output==="html"){let a=Je(t,n);s=x.makeSpan(["katex"],[a])}else{let a=jt(t,e,n,r.displayMode,!1),o=Je(t,n);s=x.makeSpan(["katex"],[a,o])}return Kt(s,r)},i1=function(t,e,r){let n=Zt(r),s=Je(t,n),a=x.makeSpan(["katex"],[s]);return Kt(a,r)};var ns=null;let a1={widehat:"^",widecheck:"\u02C7",widetilde:"~",utilde:"~",overleftarrow:"\u2190",underleftarrow:"\u2190",xleftarrow:"\u2190",overrightarrow:"\u2192",underrightarrow:"\u2192",xrightarrow:"\u2192",underbrace:"\u23DF",overbrace:"\u23DE",overgroup:"\u23E0",undergroup:"\u23E1",overleftrightarrow:"\u2194",underleftrightarrow:"\u2194",xleftrightarrow:"\u2194",Overrightarrow:"\u21D2",xRightarrow:"\u21D2",overleftharpoon:"\u21BC",xleftharpoonup:"\u21BC",overrightharpoon:"\u21C0",xrightharpoonup:"\u21C0",xLeftarrow:"\u21D0",xLeftrightarrow:"\u21D4",xhookleftarrow:"\u21A9",xhookrightarrow:"\u21AA",xmapsto:"\u21A6",xrightharpoondown:"\u21C1",xleftharpoondown:"\u21BD",xrightleftharpoons:"\u21CC",xleftrightharpoons:"\u21CB",xtwoheadleftarrow:"\u219E",xtwoheadrightarrow:"\u21A0",xlongequal:"=",xtofrom:"\u21C4",xrightleftarrows:"\u21C4",xrightequilibrium:"\u21CC",xleftequilibrium:"\u21CB","\\cdrightarrow":"\u2192","\\cdleftarrow":"\u2190","\\cdlongequal":"="},l1=function(t){let e=new z.MathNode("mo",[new z.TextNode(a1[t.replace(/^\\/,"")])]);return e.setAttribute("stretchy","true"),e},o1={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},c1=function(t){return t.type==="ordgroup"?t.body.length:1};var U0={encloseSpan:function(t,e,r,n,s){let a,o=t.height+t.depth+r+n;if(/fbox|color|angl/.test(e)){if(a=x.makeSpan(["stretchy",e],[],s),e==="fbox"){let u=s.color&&s.getColor();u&&(a.style.borderColor=u)}}else{let u=[];/^[bx]cancel$/.test(e)&&u.push(new We({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(e)&&u.push(new We({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));let m=new P0(u,{width:"100%",height:A(o)});a=x.makeSvgSpan([],[m],s)}return a.height=o,a.style.height=A(o),a},mathMLnode:l1,svgSpan:function(t,e){function r(){let o=4e5,u=t.label.slice(1);if(T.contains(["widehat","widecheck","widetilde","utilde"],u)){let f=c1(t.base),b,y,w;if(f>5)u==="widehat"||u==="widecheck"?(b=420,o=2364,w=.42,y=u+"4"):(b=312,o=2340,w=.34,y="tilde4");else{let q=[1,1,2,2,3,3][f];u==="widehat"||u==="widecheck"?(o=[0,1062,2364,2364,2364][q],b=[0,239,300,360,420][q],w=[0,.24,.3,.3,.36,.42][q],y=u+q):(o=[0,600,1033,2339,2340][q],b=[0,260,286,306,312][q],w=[0,.26,.286,.3,.306,.34][q],y="tilde"+q)}let S=new X0(y),D=new P0([S],{width:"100%",height:A(w),viewBox:"0 0 "+o+" "+b,preserveAspectRatio:"none"});return{span:x.makeSvgSpan([],[D],e),minWidth:0,height:w}}else{let m=[],f=o1[u],[b,y,w]=f,S=w/1e3,D=b.length,q,V;if(D===1){let _=f[3];q=["hide-tail"],V=[_]}else if(D===2)q=["halfarrow-left","halfarrow-right"],V=["xMinYMin","xMaxYMin"];else if(D===3)q=["brace-left","brace-center","brace-right"],V=["xMinYMin","xMidYMin","xMaxYMin"];else throw new Error(`Correct katexImagesData or update code here to support
                    `+D+" children.");for(let _=0;_<D;_++){let G=new X0(b[_]),Y=new P0([G],{width:"400em",height:A(S),viewBox:"0 0 "+o+" "+w,preserveAspectRatio:V[_]+" slice"}),K=x.makeSvgSpan([q[_]],[Y],e);if(D===1)return{span:K,minWidth:y,height:S};K.style.height=A(S),m.push(K)}return{span:x.makeSpan(["stretchy"],m,e),minWidth:y,height:S}}}let{span:n,minWidth:s,height:a}=r();return n.height=a,n.style.height=A(a),s>0&&(n.style.minWidth=A(s)),n}};function F(t,e){if(!t||t.type!==e)throw new Error("Expected node of type "+e+", but got "+(t?"node of type "+t.type:String(t)));return t}function rt(t){let e=De(t);if(!e)throw new Error("Expected node of symbol group type, but got "+(t?"node of type "+t.type:String(t)));return e}function De(t){return t&&(t.type==="atom"||Ln.hasOwnProperty(t.type))?t:null}let nt=(t,e)=>{let r,n,s;t&&t.type==="supsub"?(n=F(t.base,"accent"),r=n.base,t.base=r,s=On(U(t,e)),t.base=n):(n=F(t,"accent"),r=n.base);let a=U(r,e.havingCrampedStyle()),o=n.isShifty&&T.isCharacterBox(r),u=0;if(o){let w=T.getBaseElem(r),S=U(w,e.havingCrampedStyle());u=Ht(S).skew}let m=n.label==="\\c",f=m?a.height+a.depth:Math.min(a.height,e.fontMetrics().xHeight),b;if(n.isStretchy)b=U0.svgSpan(n,e),b=x.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:a},{type:"elem",elem:b,wrapperClasses:["svg-align"],wrapperStyle:u>0?{width:"calc(100% - "+A(2*u)+")",marginLeft:A(2*u)}:void 0}]},e);else{let w,S;n.label==="\\vec"?(w=x.staticSvg("vec",e),S=x.svgData.vec[1]):(w=x.makeOrd({mode:n.mode,text:n.label},e,"textord"),w=Ht(w),w.italic=0,S=w.width,m&&(f+=w.depth)),b=x.makeSpan(["accent-body"],[w]);let D=n.label==="\\textcircled";D&&(b.classes.push("accent-full"),f=a.height);let q=u;D||(q-=S/2),b.style.left=A(q),n.label==="\\textcircled"&&(b.style.top=".2em"),b=x.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:a},{type:"kern",size:-f},{type:"elem",elem:b}]},e)}let y=x.makeSpan(["mord","accent"],[b],e);return s?(s.children[0]=y,s.height=Math.max(y.height,s.height),s.classes[0]="mord",s):y},Jt=(t,e)=>{let r=t.isStretchy?U0.mathMLnode(t.label):new z.MathNode("mo",[T0(t.label,t.mode)]),n=new z.MathNode("mover",[Z(t.base,e),r]);return n.setAttribute("accent","true"),n},u1=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(t=>"\\"+t).join("|"));B({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(t,e)=>{let r=Ne(e[0]),n=!u1.test(t.funcName),s=!n||t.funcName==="\\widehat"||t.funcName==="\\widetilde"||t.funcName==="\\widecheck";return{type:"accent",mode:t.parser.mode,label:t.funcName,isStretchy:n,isShifty:s,base:r}},htmlBuilder:nt,mathmlBuilder:Jt}),B({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(t,e)=>{let r=e[0],n=t.parser.mode;return n==="math"&&(t.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+t.funcName+" works only in text mode"),n="text"),{type:"accent",mode:n,label:t.funcName,isStretchy:!1,isShifty:!0,base:r}},htmlBuilder:nt,mathmlBuilder:Jt}),B({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(t,e)=>{let{parser:r,funcName:n}=t,s=e[0];return{type:"accentUnder",mode:r.mode,label:n,base:s}},htmlBuilder:(t,e)=>{let r=U(t.base,e),n=U0.svgSpan(t,e),s=t.label==="\\utilde"?.12:0,a=x.makeVList({positionType:"top",positionData:r.height,children:[{type:"elem",elem:n,wrapperClasses:["svg-align"]},{type:"kern",size:s},{type:"elem",elem:r}]},e);return x.makeSpan(["mord","accentunder"],[a],e)},mathmlBuilder:(t,e)=>{let r=U0.mathMLnode(t.label),n=new z.MathNode("munder",[Z(t.base,e),r]);return n.setAttribute("accentunder","true"),n}});let qe=t=>{let e=new z.MathNode("mpadded",t?[t]:[]);return e.setAttribute("width","+0.6em"),e.setAttribute("lspace","0.3em"),e};B({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(t,e,r){let{parser:n,funcName:s}=t;return{type:"xArrow",mode:n.mode,label:s,body:e[0],below:r[0]}},htmlBuilder(t,e){let r=e.style,n=e.havingStyle(r.sup()),s=x.wrapFragment(U(t.body,n,e),e),a=t.label.slice(0,2)==="\\x"?"x":"cd";s.classes.push(a+"-arrow-pad");let o;t.below&&(n=e.havingStyle(r.sub()),o=x.wrapFragment(U(t.below,n,e),e),o.classes.push(a+"-arrow-pad"));let u=U0.svgSpan(t,e),m=-e.fontMetrics().axisHeight+.5*u.height,f=-e.fontMetrics().axisHeight-.5*u.height-.111;(s.depth>.25||t.label==="\\xleftequilibrium")&&(f-=s.depth);let b;if(o){let y=-e.fontMetrics().axisHeight+o.height+.5*u.height+.111;b=x.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:f},{type:"elem",elem:u,shift:m},{type:"elem",elem:o,shift:y}]},e)}else b=x.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:f},{type:"elem",elem:u,shift:m}]},e);return b.children[0].children[0].children[1].classes.push("svg-align"),x.makeSpan(["mrel","x-arrow"],[b],e)},mathmlBuilder(t,e){let r=U0.mathMLnode(t.label);r.setAttribute("minsize",t.label.charAt(0)==="x"?"1.75em":"3.0em");let n;if(t.body){let s=qe(Z(t.body,e));if(t.below){let a=qe(Z(t.below,e));n=new z.MathNode("munderover",[r,a,s])}else n=new z.MathNode("mover",[r,s])}else if(t.below){let s=qe(Z(t.below,e));n=new z.MathNode("munder",[r,s])}else n=qe(),n=new z.MathNode("mover",[r,n]);return n}});let h1=x.makeSpan;function Qt(t,e){let r=d0(t.body,e,!0);return h1([t.mclass],r,e)}function er(t,e){let r,n=w0(t.body,e);return t.mclass==="minner"?r=new z.MathNode("mpadded",n):t.mclass==="mord"?t.isCharacterBox?(r=n[0],r.type="mi"):r=new z.MathNode("mi",n):(t.isCharacterBox?(r=n[0],r.type="mo"):r=new z.MathNode("mo",n),t.mclass==="mbin"?(r.attributes.lspace="0.22em",r.attributes.rspace="0.22em"):t.mclass==="mpunct"?(r.attributes.lspace="0em",r.attributes.rspace="0.17em"):t.mclass==="mopen"||t.mclass==="mclose"?(r.attributes.lspace="0em",r.attributes.rspace="0em"):t.mclass==="minner"&&(r.attributes.lspace="0.0556em",r.attributes.width="+0.1111em")),r}B({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(t,e){let{parser:r,funcName:n}=t,s=e[0];return{type:"mclass",mode:r.mode,mclass:"m"+n.slice(5),body:u0(s),isCharacterBox:T.isCharacterBox(s)}},htmlBuilder:Qt,mathmlBuilder:er});let Ee=t=>{let e=t.type==="ordgroup"&&t.body.length?t.body[0]:t;return e.type==="atom"&&(e.family==="bin"||e.family==="rel")?"m"+e.family:"mord"};B({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(t,e){let{parser:r}=t;return{type:"mclass",mode:r.mode,mclass:Ee(e[0]),body:u0(e[1]),isCharacterBox:T.isCharacterBox(e[1])}}}),B({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(t,e){let{parser:r,funcName:n}=t,s=e[1],a=e[0],o;n!=="\\stackrel"?o=Ee(s):o="mrel";let u={type:"op",mode:s.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:n!=="\\stackrel",body:u0(s)},m={type:"supsub",mode:a.mode,base:u,sup:n==="\\underset"?null:a,sub:n==="\\underset"?a:null};return{type:"mclass",mode:r.mode,mclass:o,body:[m],isCharacterBox:T.isCharacterBox(m)}},htmlBuilder:Qt,mathmlBuilder:er}),B({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(t,e){let{parser:r}=t;return{type:"pmb",mode:r.mode,mclass:Ee(e[0]),body:u0(e[0])}},htmlBuilder(t,e){let r=d0(t.body,e,!0),n=x.makeSpan([t.mclass],r,e);return n.style.textShadow="0.02em 0.01em 0.04px",n},mathmlBuilder(t,e){let r=w0(t.body,e),n=new z.MathNode("mstyle",r);return n.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),n}});let m1={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},tr=()=>({type:"styling",body:[],mode:"math",style:"display"}),rr=t=>t.type==="textord"&&t.text==="@",d1=(t,e)=>(t.type==="mathord"||t.type==="atom")&&t.text===e;function p1(t,e,r){let n=m1[t];switch(n){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return r.callFunction(n,[e[0]],[e[1]]);case"\\uparrow":case"\\downarrow":{let s=r.callFunction("\\\\cdleft",[e[0]],[]),a={type:"atom",text:n,mode:"math",family:"rel"},o=r.callFunction("\\Big",[a],[]),u=r.callFunction("\\\\cdright",[e[1]],[]),m={type:"ordgroup",mode:"math",body:[s,o,u]};return r.callFunction("\\\\cdparent",[m],[])}case"\\\\cdlongequal":return r.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":{let s={type:"textord",text:"\\Vert",mode:"math"};return r.callFunction("\\Big",[s],[])}default:return{type:"textord",text:" ",mode:"math"}}}function f1(t){let e=[];for(t.gullet.beginGroup(),t.gullet.macros.set("\\cr","\\\\\\relax"),t.gullet.beginGroup();;){e.push(t.parseExpression(!1,"\\\\")),t.gullet.endGroup(),t.gullet.beginGroup();let a=t.fetch().text;if(a==="&"||a==="\\\\")t.consume();else if(a==="\\end"){e[e.length-1].length===0&&e.pop();break}else throw new v("Expected \\\\ or \\cr or \\end",t.nextToken)}let r=[],n=[r];for(let a=0;a<e.length;a++){let o=e[a],u=tr();for(let m=0;m<o.length;m++)if(!rr(o[m]))u.body.push(o[m]);else{r.push(u),m+=1;let f=rt(o[m]).text,b=new Array(2);if(b[0]={type:"ordgroup",mode:"math",body:[]},b[1]={type:"ordgroup",mode:"math",body:[]},!("=|.".indexOf(f)>-1))if("<>AV".indexOf(f)>-1)for(let S=0;S<2;S++){let D=!0;for(let q=m+1;q<o.length;q++){if(d1(o[q],f)){D=!1,m=q;break}if(rr(o[q]))throw new v("Missing a "+f+" character to complete a CD arrow.",o[q]);b[S].body.push(o[q])}if(D)throw new v("Missing a "+f+" character to complete a CD arrow.",o[m])}else throw new v('Expected one of "<>AV=|." after @',o[m]);let w={type:"styling",body:[p1(f,b,t)],mode:"math",style:"display"};r.push(w),u=tr()}a%2===0?r.push(u):r.shift(),r=[],n.push(r)}t.gullet.endGroup(),t.gullet.endGroup();let s=new Array(n[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:n,arraystretch:1,addJot:!0,rowGaps:[null],cols:s,colSeparationType:"CD",hLinesBeforeRow:new Array(n.length+1).fill([])}}B({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(t,e){let{parser:r,funcName:n}=t;return{type:"cdlabel",mode:r.mode,side:n.slice(4),label:e[0]}},htmlBuilder(t,e){let r=e.havingStyle(e.style.sup()),n=x.wrapFragment(U(t.label,r,e),e);return n.classes.push("cd-label-"+t.side),n.style.bottom=A(.8-n.depth),n.height=0,n.depth=0,n},mathmlBuilder(t,e){let r=new z.MathNode("mrow",[Z(t.label,e)]);return r=new z.MathNode("mpadded",[r]),r.setAttribute("width","0"),t.side==="left"&&r.setAttribute("lspace","-1width"),r.setAttribute("voffset","0.7em"),r=new z.MathNode("mstyle",[r]),r.setAttribute("displaystyle","false"),r.setAttribute("scriptlevel","1"),r}}),B({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(t,e){let{parser:r}=t;return{type:"cdlabelparent",mode:r.mode,fragment:e[0]}},htmlBuilder(t,e){let r=x.wrapFragment(U(t.fragment,e),e);return r.classes.push("cd-vert-arrow"),r},mathmlBuilder(t,e){return new z.MathNode("mrow",[Z(t.fragment,e)])}}),B({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(t,e){let{parser:r}=t,s=F(e[0],"ordgroup").body,a="";for(let m=0;m<s.length;m++){let f=F(s[m],"textord");a+=f.text}let o=parseInt(a),u;if(isNaN(o))throw new v("\\@char has non-numeric argument "+a);if(o<0||o>=1114111)throw new v("\\@char with invalid code point "+a);return o<=65535?u=String.fromCharCode(o):(o-=65536,u=String.fromCharCode((o>>10)+55296,(o&1023)+56320)),{type:"textord",mode:r.mode,text:u}}});let nr=(t,e)=>{let r=d0(t.body,e.withColor(t.color),!1);return x.makeFragment(r)},sr=(t,e)=>{let r=w0(t.body,e.withColor(t.color)),n=new z.MathNode("mstyle",r);return n.setAttribute("mathcolor",t.color),n};B({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(t,e){let{parser:r}=t,n=F(e[0],"color-token").color,s=e[1];return{type:"color",mode:r.mode,color:n,body:u0(s)}},htmlBuilder:nr,mathmlBuilder:sr}),B({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(t,e){let{parser:r,breakOnTokenText:n}=t,s=F(e[0],"color-token").color;r.gullet.macros.set("\\current@color",s);let a=r.parseExpression(!0,n);return{type:"color",mode:r.mode,color:s,body:a}},htmlBuilder:nr,mathmlBuilder:sr}),B({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(t,e,r){let{parser:n}=t,s=n.gullet.future().text==="["?n.parseSizeGroup(!0):null,a=!n.settings.displayMode||!n.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:n.mode,newLine:a,size:s&&F(s,"size").value}},htmlBuilder(t,e){let r=x.makeSpan(["mspace"],[],e);return t.newLine&&(r.classes.push("newline"),t.size&&(r.style.marginTop=A(n0(t.size,e)))),r},mathmlBuilder(t,e){let r=new z.MathNode("mspace");return t.newLine&&(r.setAttribute("linebreak","newline"),t.size&&r.setAttribute("height",A(n0(t.size,e)))),r}});let st={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},ir=t=>{let e=t.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(e))throw new v("Expected a control sequence",t);return e},g1=t=>{let e=t.gullet.popToken();return e.text==="="&&(e=t.gullet.popToken(),e.text===" "&&(e=t.gullet.popToken())),e},ar=(t,e,r,n)=>{let s=t.gullet.macros.get(r.text);s==null&&(r.noexpand=!0,s={tokens:[r],numArgs:0,unexpandable:!t.gullet.isExpandable(r.text)}),t.gullet.macros.set(e,s,n)};B({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(t){let{parser:e,funcName:r}=t;e.consumeSpaces();let n=e.fetch();if(st[n.text])return(r==="\\global"||r==="\\\\globallong")&&(n.text=st[n.text]),F(e.parseFunction(),"internal");throw new v("Invalid token after macro prefix",n)}}),B({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t){let{parser:e,funcName:r}=t,n=e.gullet.popToken(),s=n.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(s))throw new v("Expected a control sequence",n);let a=0,o,u=[[]];for(;e.gullet.future().text!=="{";)if(n=e.gullet.popToken(),n.text==="#"){if(e.gullet.future().text==="{"){o=e.gullet.future(),u[a].push("{");break}if(n=e.gullet.popToken(),!/^[1-9]$/.test(n.text))throw new v('Invalid argument number "'+n.text+'"');if(parseInt(n.text)!==a+1)throw new v('Argument number "'+n.text+'" out of order');a++,u.push([])}else{if(n.text==="EOF")throw new v("Expected a macro definition");u[a].push(n.text)}let{tokens:m}=e.gullet.consumeArg();return o&&m.unshift(o),(r==="\\edef"||r==="\\xdef")&&(m=e.gullet.expandTokens(m),m.reverse()),e.gullet.macros.set(s,{tokens:m,numArgs:a,delimiters:u},r===st[r]),{type:"internal",mode:e.mode}}}),B({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t){let{parser:e,funcName:r}=t,n=ir(e.gullet.popToken());e.gullet.consumeSpaces();let s=g1(e);return ar(e,n,s,r==="\\\\globallet"),{type:"internal",mode:e.mode}}}),B({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t){let{parser:e,funcName:r}=t,n=ir(e.gullet.popToken()),s=e.gullet.popToken(),a=e.gullet.popToken();return ar(e,n,a,r==="\\\\globalfuture"),e.gullet.pushToken(a),e.gullet.pushToken(s),{type:"internal",mode:e.mode}}});let de=function(t,e,r){let n=s0.math[t]&&s0.math[t].replace,s=Ve(n||t,e,r);if(!s)throw new Error("Unsupported symbol "+t+" and font size "+e+".");return s},it=function(t,e,r,n){let s=r.havingBaseStyle(e),a=x.makeSpan(n.concat(s.sizingClasses(r)),[t],r),o=s.sizeMultiplier/r.sizeMultiplier;return a.height*=o,a.depth*=o,a.maxFontSize=s.sizeMultiplier,a},lr=function(t,e,r){let n=e.havingBaseStyle(r),s=(1-e.sizeMultiplier/n.sizeMultiplier)*e.fontMetrics().axisHeight;t.classes.push("delimcenter"),t.style.top=A(s),t.height-=s,t.depth+=s},b1=function(t,e,r,n,s,a){let o=x.makeSymbol(t,"Main-Regular",s,n),u=it(o,e,n,a);return r&&lr(u,n,e),u},y1=function(t,e,r,n){return x.makeSymbol(t,"Size"+e+"-Regular",r,n)},or=function(t,e,r,n,s,a){let o=y1(t,e,s,n),u=it(x.makeSpan(["delimsizing","size"+e],[o],n),E.TEXT,n,a);return r&&lr(u,n,E.TEXT),u},at=function(t,e,r){let n;return e==="Size1-Regular"?n="delim-size1":n="delim-size4",{type:"elem",elem:x.makeSpan(["delimsizinginner",n],[x.makeSpan([],[x.makeSymbol(t,e,r)])])}},lt=function(t,e,r){let n=I0["Size4-Regular"][t.charCodeAt(0)]?I0["Size4-Regular"][t.charCodeAt(0)][4]:I0["Size1-Regular"][t.charCodeAt(0)][4],s=new X0("inner",An(t,Math.round(1e3*e))),a=new P0([s],{width:A(n),height:A(e),style:"width:"+A(n),viewBox:"0 0 "+1e3*n+" "+Math.round(1e3*e),preserveAspectRatio:"xMinYMin"}),o=x.makeSvgSpan([],[a],r);return o.height=e,o.style.height=A(e),o.style.width=A(n),{type:"elem",elem:o}},ot=.008,Ie={type:"kern",size:-1*ot},x1=["|","\\lvert","\\rvert","\\vert"],w1=["\\|","\\lVert","\\rVert","\\Vert"],cr=function(t,e,r,n,s,a){let o,u,m,f,b="",y=0;o=m=f=t,u=null;let w="Size1-Regular";t==="\\uparrow"?m=f="\u23D0":t==="\\Uparrow"?m=f="\u2016":t==="\\downarrow"?o=m="\u23D0":t==="\\Downarrow"?o=m="\u2016":t==="\\updownarrow"?(o="\\uparrow",m="\u23D0",f="\\downarrow"):t==="\\Updownarrow"?(o="\\Uparrow",m="\u2016",f="\\Downarrow"):T.contains(x1,t)?(m="\u2223",b="vert",y=333):T.contains(w1,t)?(m="\u2225",b="doublevert",y=556):t==="["||t==="\\lbrack"?(o="\u23A1",m="\u23A2",f="\u23A3",w="Size4-Regular",b="lbrack",y=667):t==="]"||t==="\\rbrack"?(o="\u23A4",m="\u23A5",f="\u23A6",w="Size4-Regular",b="rbrack",y=667):t==="\\lfloor"||t==="\u230A"?(m=o="\u23A2",f="\u23A3",w="Size4-Regular",b="lfloor",y=667):t==="\\lceil"||t==="\u2308"?(o="\u23A1",m=f="\u23A2",w="Size4-Regular",b="lceil",y=667):t==="\\rfloor"||t==="\u230B"?(m=o="\u23A5",f="\u23A6",w="Size4-Regular",b="rfloor",y=667):t==="\\rceil"||t==="\u2309"?(o="\u23A4",m=f="\u23A5",w="Size4-Regular",b="rceil",y=667):t==="("||t==="\\lparen"?(o="\u239B",m="\u239C",f="\u239D",w="Size4-Regular",b="lparen",y=875):t===")"||t==="\\rparen"?(o="\u239E",m="\u239F",f="\u23A0",w="Size4-Regular",b="rparen",y=875):t==="\\{"||t==="\\lbrace"?(o="\u23A7",u="\u23A8",f="\u23A9",m="\u23AA",w="Size4-Regular"):t==="\\}"||t==="\\rbrace"?(o="\u23AB",u="\u23AC",f="\u23AD",m="\u23AA",w="Size4-Regular"):t==="\\lgroup"||t==="\u27EE"?(o="\u23A7",f="\u23A9",m="\u23AA",w="Size4-Regular"):t==="\\rgroup"||t==="\u27EF"?(o="\u23AB",f="\u23AD",m="\u23AA",w="Size4-Regular"):t==="\\lmoustache"||t==="\u23B0"?(o="\u23A7",f="\u23AD",m="\u23AA",w="Size4-Regular"):(t==="\\rmoustache"||t==="\u23B1")&&(o="\u23AB",f="\u23A9",m="\u23AA",w="Size4-Regular");let S=de(o,w,s),D=S.height+S.depth,q=de(m,w,s),V=q.height+q.depth,_=de(f,w,s),G=_.height+_.depth,Y=0,K=1;if(u!==null){let l0=de(u,w,s);Y=l0.height+l0.depth,K=2}let y0=D+G+Y,p0=Math.max(0,Math.ceil((e-y0)/(K*V))),N0=y0+p0*K*V,ae=n.fontMetrics().axisHeight;r&&(ae*=n.sizeMultiplier);let W=N0/2-ae,X=[];if(b.length>0){let l0=N0-D-G,a0=Math.round(N0*1e3),C0=Tn(b,Math.round(l0*1e3)),W1=new X0(b,C0),Qr=(y/1e3).toFixed(3)+"em",en=(a0/1e3).toFixed(3)+"em",Y1=new P0([W1],{width:Qr,height:en,viewBox:"0 0 "+y+" "+a0}),Fe=x.makeSvgSpan([],[Y1],n);Fe.height=a0/1e3,Fe.style.width=Qr,Fe.style.height=en,X.push({type:"elem",elem:Fe})}else{if(X.push(at(f,w,s)),X.push(Ie),u===null){let l0=N0-D-G+2*ot;X.push(lt(m,l0,n))}else{let l0=(N0-D-G-Y)/2+2*ot;X.push(lt(m,l0,n)),X.push(Ie),X.push(at(u,w,s)),X.push(Ie),X.push(lt(m,l0,n))}X.push(Ie),X.push(at(o,w,s))}let J=n.havingBaseStyle(E.TEXT),t0=x.makeVList({positionType:"bottom",positionData:W,children:X},J);return it(x.makeSpan(["delimsizing","mult"],[t0],J),E.TEXT,n,a)},ct=80,ut=.08,ht=function(t,e,r,n,s){let a=zn(t,n,r),o=new X0(t,a),u=new P0([o],{width:"400em",height:A(e),viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return x.makeSvgSpan(["hide-tail"],[u],s)},k1=function(t,e){let r=e.havingBaseSizing(),n=dr("\\surd",t*r.sizeMultiplier,mr,r),s=r.sizeMultiplier,a=Math.max(0,e.minRuleThickness-e.fontMetrics().sqrtRuleThickness),o,u=0,m=0,f=0,b;return n.type==="small"?(f=1e3+1e3*a+ct,t<1?s=1:t<1.4&&(s=.7),u=(1+a+ut)/s,m=(1+a)/s,o=ht("sqrtMain",u,f,a,e),o.style.minWidth="0.853em",b=.833/s):n.type==="large"?(f=(1e3+ct)*pe[n.size],m=(pe[n.size]+a)/s,u=(pe[n.size]+a+ut)/s,o=ht("sqrtSize"+n.size,u,f,a,e),o.style.minWidth="1.02em",b=1/s):(u=t+a+ut,m=t+a,f=Math.floor(1e3*t+a)+ct,o=ht("sqrtTall",u,f,a,e),o.style.minWidth="0.742em",b=1.056),o.height=m,o.style.height=A(u),{span:o,advanceWidth:b,ruleWidth:(e.fontMetrics().sqrtRuleThickness+a)*s}},ur=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230A","\u230B","\\lceil","\\rceil","\u2308","\u2309","\\surd"],v1=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27EE","\u27EF","\\lmoustache","\\rmoustache","\u23B0","\u23B1"],hr=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],pe=[0,1.2,1.8,2.4,3],S1=function(t,e,r,n,s){if(t==="<"||t==="\\lt"||t==="\u27E8"?t="\\langle":(t===">"||t==="\\gt"||t==="\u27E9")&&(t="\\rangle"),T.contains(ur,t)||T.contains(hr,t))return or(t,e,!1,r,n,s);if(T.contains(v1,t))return cr(t,pe[e],!1,r,n,s);throw new v("Illegal delimiter: '"+t+"'")},M1=[{type:"small",style:E.SCRIPTSCRIPT},{type:"small",style:E.SCRIPT},{type:"small",style:E.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],z1=[{type:"small",style:E.SCRIPTSCRIPT},{type:"small",style:E.SCRIPT},{type:"small",style:E.TEXT},{type:"stack"}],mr=[{type:"small",style:E.SCRIPTSCRIPT},{type:"small",style:E.SCRIPT},{type:"small",style:E.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],A1=function(t){if(t.type==="small")return"Main-Regular";if(t.type==="large")return"Size"+t.size+"-Regular";if(t.type==="stack")return"Size4-Regular";throw new Error("Add support for delim type '"+t.type+"' here.")},dr=function(t,e,r,n){let s=Math.min(2,3-n.style.size);for(let a=s;a<r.length&&r[a].type!=="stack";a++){let o=de(t,A1(r[a]),"math"),u=o.height+o.depth;if(r[a].type==="small"){let m=n.havingBaseStyle(r[a].style);u*=m.sizeMultiplier}if(u>e)return r[a]}return r[r.length-1]},pr=function(t,e,r,n,s,a){t==="<"||t==="\\lt"||t==="\u27E8"?t="\\langle":(t===">"||t==="\\gt"||t==="\u27E9")&&(t="\\rangle");let o;T.contains(hr,t)?o=M1:T.contains(ur,t)?o=mr:o=z1;let u=dr(t,e,o,n);return u.type==="small"?b1(t,u.style,r,n,s,a):u.type==="large"?or(t,u.size,r,n,s,a):cr(t,e,r,n,s,a)};var W0={sqrtImage:k1,sizedDelim:S1,sizeToMaxHeight:pe,customSizedDelim:pr,leftRightDelim:function(t,e,r,n,s,a){let o=n.fontMetrics().axisHeight*n.sizeMultiplier,u=901,m=5/n.fontMetrics().ptPerEm,f=Math.max(e-o,r+o),b=Math.max(f/500*u,2*f-m);return pr(t,b,!0,n,s,a)}};let fr={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},T1=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230A","\u230B","\\lceil","\\rceil","\u2308","\u2309","<",">","\\langle","\u27E8","\\rangle","\u27E9","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27EE","\u27EF","\\lmoustache","\\rmoustache","\u23B0","\u23B1","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function Re(t,e){let r=De(t);if(r&&T.contains(T1,r.text))return r;throw r?new v("Invalid delimiter '"+r.text+"' after '"+e.funcName+"'",t):new v("Invalid delimiter type '"+t.type+"'",t)}B({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(t,e)=>{let r=Re(e[0],t);return{type:"delimsizing",mode:t.parser.mode,size:fr[t.funcName].size,mclass:fr[t.funcName].mclass,delim:r.text}},htmlBuilder:(t,e)=>t.delim==="."?x.makeSpan([t.mclass]):W0.sizedDelim(t.delim,t.size,e,t.mode,[t.mclass]),mathmlBuilder:t=>{let e=[];t.delim!=="."&&e.push(T0(t.delim,t.mode));let r=new z.MathNode("mo",e);t.mclass==="mopen"||t.mclass==="mclose"?r.setAttribute("fence","true"):r.setAttribute("fence","false"),r.setAttribute("stretchy","true");let n=A(W0.sizeToMaxHeight[t.size]);return r.setAttribute("minsize",n),r.setAttribute("maxsize",n),r}});function gr(t){if(!t.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}B({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{let r=t.parser.gullet.macros.get("\\current@color");if(r&&typeof r!="string")throw new v("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:t.parser.mode,delim:Re(e[0],t).text,color:r}}}),B({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{let r=Re(e[0],t),n=t.parser;++n.leftrightDepth;let s=n.parseExpression(!1);--n.leftrightDepth,n.expect("\\right",!1);let a=F(n.parseFunction(),"leftright-right");return{type:"leftright",mode:n.mode,body:s,left:r.text,right:a.delim,rightColor:a.color}},htmlBuilder:(t,e)=>{gr(t);let r=d0(t.body,e,!0,["mopen","mclose"]),n=0,s=0,a=!1;for(let m=0;m<r.length;m++)r[m].isMiddle?a=!0:(n=Math.max(r[m].height,n),s=Math.max(r[m].depth,s));n*=e.sizeMultiplier,s*=e.sizeMultiplier;let o;if(t.left==="."?o=me(e,["mopen"]):o=W0.leftRightDelim(t.left,n,s,e,t.mode,["mopen"]),r.unshift(o),a)for(let m=1;m<r.length;m++){let b=r[m].isMiddle;b&&(r[m]=W0.leftRightDelim(b.delim,n,s,b.options,t.mode,[]))}let u;if(t.right===".")u=me(e,["mclose"]);else{let m=t.rightColor?e.withColor(t.rightColor):e;u=W0.leftRightDelim(t.right,n,s,m,t.mode,["mclose"])}return r.push(u),x.makeSpan(["minner"],r,e)},mathmlBuilder:(t,e)=>{gr(t);let r=w0(t.body,e);if(t.left!=="."){let n=new z.MathNode("mo",[T0(t.left,t.mode)]);n.setAttribute("fence","true"),r.unshift(n)}if(t.right!=="."){let n=new z.MathNode("mo",[T0(t.right,t.mode)]);n.setAttribute("fence","true"),t.rightColor&&n.setAttribute("mathcolor",t.rightColor),r.push(n)}return Qe(r)}}),B({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{let r=Re(e[0],t);if(!t.parser.leftrightDepth)throw new v("\\middle without preceding \\left",r);return{type:"middle",mode:t.parser.mode,delim:r.text}},htmlBuilder:(t,e)=>{let r;if(t.delim===".")r=me(e,[]);else{r=W0.sizedDelim(t.delim,1,e,t.mode,[]);let n={delim:t.delim,options:e};r.isMiddle=n}return r},mathmlBuilder:(t,e)=>{let r=t.delim==="\\vert"||t.delim==="|"?T0("|","text"):T0(t.delim,t.mode),n=new z.MathNode("mo",[r]);return n.setAttribute("fence","true"),n.setAttribute("lspace","0.05em"),n.setAttribute("rspace","0.05em"),n}});let mt=(t,e)=>{let r=x.wrapFragment(U(t.body,e),e),n=t.label.slice(1),s=e.sizeMultiplier,a,o=0,u=T.isCharacterBox(t.body);if(n==="sout")a=x.makeSpan(["stretchy","sout"]),a.height=e.fontMetrics().defaultRuleThickness/s,o=-.5*e.fontMetrics().xHeight;else if(n==="phase"){let f=n0({number:.6,unit:"pt"},e),b=n0({number:.35,unit:"ex"},e),y=e.havingBaseSizing();s=s/y.sizeMultiplier;let w=r.height+r.depth+f+b;r.style.paddingLeft=A(w/2+f);let S=Math.floor(1e3*w*s),D=Sn(S),q=new P0([new X0("phase",D)],{width:"400em",height:A(S/1e3),viewBox:"0 0 400000 "+S,preserveAspectRatio:"xMinYMin slice"});a=x.makeSvgSpan(["hide-tail"],[q],e),a.style.height=A(w),o=r.depth+f+b}else{/cancel/.test(n)?u||r.classes.push("cancel-pad"):n==="angl"?r.classes.push("anglpad"):r.classes.push("boxpad");let f=0,b=0,y=0;/box/.test(n)?(y=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness),f=e.fontMetrics().fboxsep+(n==="colorbox"?0:y),b=f):n==="angl"?(y=Math.max(e.fontMetrics().defaultRuleThickness,e.minRuleThickness),f=4*y,b=Math.max(0,.25-r.depth)):(f=u?.2:0,b=f),a=U0.encloseSpan(r,n,f,b,e),/fbox|boxed|fcolorbox/.test(n)?(a.style.borderStyle="solid",a.style.borderWidth=A(y)):n==="angl"&&y!==.049&&(a.style.borderTopWidth=A(y),a.style.borderRightWidth=A(y)),o=r.depth+b,t.backgroundColor&&(a.style.backgroundColor=t.backgroundColor,t.borderColor&&(a.style.borderColor=t.borderColor))}let m;if(t.backgroundColor)m=x.makeVList({positionType:"individualShift",children:[{type:"elem",elem:a,shift:o},{type:"elem",elem:r,shift:0}]},e);else{let f=/cancel|phase/.test(n)?["svg-align"]:[];m=x.makeVList({positionType:"individualShift",children:[{type:"elem",elem:r,shift:0},{type:"elem",elem:a,shift:o,wrapperClasses:f}]},e)}return/cancel/.test(n)&&(m.height=r.height,m.depth=r.depth),/cancel/.test(n)&&!u?x.makeSpan(["mord","cancel-lap"],[m],e):x.makeSpan(["mord"],[m],e)},dt=(t,e)=>{let r=0,n=new z.MathNode(t.label.indexOf("colorbox")>-1?"mpadded":"menclose",[Z(t.body,e)]);switch(t.label){case"\\cancel":n.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":n.setAttribute("notation","downdiagonalstrike");break;case"\\phase":n.setAttribute("notation","phasorangle");break;case"\\sout":n.setAttribute("notation","horizontalstrike");break;case"\\fbox":n.setAttribute("notation","box");break;case"\\angl":n.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(r=e.fontMetrics().fboxsep*e.fontMetrics().ptPerEm,n.setAttribute("width","+"+2*r+"pt"),n.setAttribute("height","+"+2*r+"pt"),n.setAttribute("lspace",r+"pt"),n.setAttribute("voffset",r+"pt"),t.label==="\\fcolorbox"){let s=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness);n.setAttribute("style","border: "+s+"em solid "+String(t.borderColor))}break;case"\\xcancel":n.setAttribute("notation","updiagonalstrike downdiagonalstrike");break}return t.backgroundColor&&n.setAttribute("mathbackground",t.backgroundColor),n};B({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(t,e,r){let{parser:n,funcName:s}=t,a=F(e[0],"color-token").color,o=e[1];return{type:"enclose",mode:n.mode,label:s,backgroundColor:a,body:o}},htmlBuilder:mt,mathmlBuilder:dt}),B({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(t,e,r){let{parser:n,funcName:s}=t,a=F(e[0],"color-token").color,o=F(e[1],"color-token").color,u=e[2];return{type:"enclose",mode:n.mode,label:s,backgroundColor:o,borderColor:a,body:u}},htmlBuilder:mt,mathmlBuilder:dt}),B({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(t,e){let{parser:r}=t;return{type:"enclose",mode:r.mode,label:"\\fbox",body:e[0]}}}),B({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(t,e){let{parser:r,funcName:n}=t,s=e[0];return{type:"enclose",mode:r.mode,label:n,body:s}},htmlBuilder:mt,mathmlBuilder:dt}),B({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(t,e){let{parser:r}=t;return{type:"enclose",mode:r.mode,label:"\\angl",body:e[0]}}});let br={};function O0(t){let{type:e,names:r,props:n,handler:s,htmlBuilder:a,mathmlBuilder:o}=t,u={type:e,numArgs:n.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:s};for(let m=0;m<r.length;++m)br[r[m]]=u;a&&(Te[e]=a),o&&(Be[e]=o)}let yr={};function h(t,e){yr[t]=e}class M0{constructor(e,r,n){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=r,this.end=n}static range(e,r){return r?!e||!e.loc||!r.loc||e.loc.lexer!==r.loc.lexer?null:new M0(e.loc.lexer,e.loc.start,r.loc.end):e&&e.loc}}class B0{constructor(e,r){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=r}range(e,r){return new B0(r,M0.range(this,e))}}function xr(t){let e=[];t.consumeSpaces();let r=t.fetch().text;for(r==="\\relax"&&(t.consume(),t.consumeSpaces(),r=t.fetch().text);r==="\\hline"||r==="\\hdashline";)t.consume(),e.push(r==="\\hdashline"),t.consumeSpaces(),r=t.fetch().text;return e}let Oe=t=>{if(!t.parser.settings.displayMode)throw new v("{"+t.envName+"} can be used only in display mode.")};function pt(t){if(t.indexOf("ed")===-1)return t.indexOf("*")===-1}function Z0(t,e,r){let{hskipBeforeAndAfter:n,addJot:s,cols:a,arraystretch:o,colSeparationType:u,autoTag:m,singleRow:f,emptySingleRow:b,maxNumCols:y,leqno:w}=e;if(t.gullet.beginGroup(),f||t.gullet.macros.set("\\cr","\\\\\\relax"),!o){let K=t.gullet.expandMacroAsText("\\arraystretch");if(K==null)o=1;else if(o=parseFloat(K),!o||o<0)throw new v("Invalid \\arraystretch: "+K)}t.gullet.beginGroup();let S=[],D=[S],q=[],V=[],_=m!=null?[]:void 0;function G(){m&&t.gullet.macros.set("\\@eqnsw","1",!0)}function Y(){_&&(t.gullet.macros.get("\\df@tag")?(_.push(t.subparse([new B0("\\df@tag")])),t.gullet.macros.set("\\df@tag",void 0,!0)):_.push(!!m&&t.gullet.macros.get("\\@eqnsw")==="1"))}for(G(),V.push(xr(t));;){let K=t.parseExpression(!1,f?"\\end":"\\\\");t.gullet.endGroup(),t.gullet.beginGroup(),K={type:"ordgroup",mode:t.mode,body:K},r&&(K={type:"styling",mode:t.mode,style:r,body:[K]}),S.push(K);let y0=t.fetch().text;if(y0==="&"){if(y&&S.length===y){if(f||u)throw new v("Too many tab characters: &",t.nextToken);t.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}t.consume()}else if(y0==="\\end"){Y(),S.length===1&&K.type==="styling"&&K.body[0].body.length===0&&(D.length>1||!b)&&D.pop(),V.length<D.length+1&&V.push([]);break}else if(y0==="\\\\"){t.consume();let p0;t.gullet.future().text!==" "&&(p0=t.parseSizeGroup(!0)),q.push(p0?p0.value:null),Y(),V.push(xr(t)),S=[],D.push(S),G()}else throw new v("Expected & or \\\\ or \\cr or \\end",t.nextToken)}return t.gullet.endGroup(),t.gullet.endGroup(),{type:"array",mode:t.mode,addJot:s,arraystretch:o,body:D,cols:a,rowGaps:q,hskipBeforeAndAfter:n,hLinesBeforeRow:V,colSeparationType:u,tags:_,leqno:w}}function ft(t){return t.slice(0,1)==="d"?"display":"text"}let H0=function(t,e){let r,n,s=t.body.length,a=t.hLinesBeforeRow,o=0,u=new Array(s),m=[],f=Math.max(e.fontMetrics().arrayRuleWidth,e.minRuleThickness),b=1/e.fontMetrics().ptPerEm,y=5*b;t.colSeparationType&&t.colSeparationType==="small"&&(y=.2778*(e.havingStyle(E.SCRIPT).sizeMultiplier/e.sizeMultiplier));let w=t.colSeparationType==="CD"?n0({number:3,unit:"ex"},e):12*b,S=3*b,D=t.arraystretch*w,q=.7*D,V=.3*D,_=0;function G(W){for(let X=0;X<W.length;++X)X>0&&(_+=.25),m.push({pos:_,isDashed:W[X]})}for(G(a[0]),r=0;r<t.body.length;++r){let W=t.body[r],X=q,J=V;o<W.length&&(o=W.length);let t0=new Array(W.length);for(n=0;n<W.length;++n){let C0=U(W[n],e);J<C0.depth&&(J=C0.depth),X<C0.height&&(X=C0.height),t0[n]=C0}let l0=t.rowGaps[r],a0=0;l0&&(a0=n0(l0,e),a0>0&&(a0+=V,J<a0&&(J=a0),a0=0)),t.addJot&&(J+=S),t0.height=X,t0.depth=J,_+=X,t0.pos=_,_+=J+a0,u[r]=t0,G(a[r+1])}let Y=_/2+e.fontMetrics().axisHeight,K=t.cols||[],y0=[],p0,N0,ae=[];if(t.tags&&t.tags.some(W=>W))for(r=0;r<s;++r){let W=u[r],X=W.pos-Y,J=t.tags[r],t0;J===!0?t0=x.makeSpan(["eqn-num"],[],e):J===!1?t0=x.makeSpan([],[],e):t0=x.makeSpan([],d0(J,e,!0),e),t0.depth=W.depth,t0.height=W.height,ae.push({type:"elem",elem:t0,shift:X})}for(n=0,N0=0;n<o||N0<K.length;++n,++N0){let W=K[N0]||{},X=!0;for(;W.type==="separator";){if(X||(p0=x.makeSpan(["arraycolsep"],[]),p0.style.width=A(e.fontMetrics().doubleRuleSep),y0.push(p0)),W.separator==="|"||W.separator===":"){let l0=W.separator==="|"?"solid":"dashed",a0=x.makeSpan(["vertical-separator"],[],e);a0.style.height=A(_),a0.style.borderRightWidth=A(f),a0.style.borderRightStyle=l0,a0.style.margin="0 "+A(-f/2);let C0=_-Y;C0&&(a0.style.verticalAlign=A(-C0)),y0.push(a0)}else throw new v("Invalid separator type: "+W.separator);N0++,W=K[N0]||{},X=!1}if(n>=o)continue;let J;(n>0||t.hskipBeforeAndAfter)&&(J=T.deflt(W.pregap,y),J!==0&&(p0=x.makeSpan(["arraycolsep"],[]),p0.style.width=A(J),y0.push(p0)));let t0=[];for(r=0;r<s;++r){let l0=u[r],a0=l0[n];if(!a0)continue;let C0=l0.pos-Y;a0.depth=l0.depth,a0.height=l0.height,t0.push({type:"elem",elem:a0,shift:C0})}t0=x.makeVList({positionType:"individualShift",children:t0},e),t0=x.makeSpan(["col-align-"+(W.align||"c")],[t0]),y0.push(t0),(n<o-1||t.hskipBeforeAndAfter)&&(J=T.deflt(W.postgap,y),J!==0&&(p0=x.makeSpan(["arraycolsep"],[]),p0.style.width=A(J),y0.push(p0)))}if(u=x.makeSpan(["mtable"],y0),m.length>0){let W=x.makeLineSpan("hline",e,f),X=x.makeLineSpan("hdashline",e,f),J=[{type:"elem",elem:u,shift:0}];for(;m.length>0;){let t0=m.pop(),l0=t0.pos-Y;t0.isDashed?J.push({type:"elem",elem:X,shift:l0}):J.push({type:"elem",elem:W,shift:l0})}u=x.makeVList({positionType:"individualShift",children:J},e)}if(ae.length===0)return x.makeSpan(["mord"],[u],e);{let W=x.makeVList({positionType:"individualShift",children:ae},e);return W=x.makeSpan(["tag"],[W],e),x.makeFragment([u,W])}},B1={c:"center ",l:"left ",r:"right "},L0=function(t,e){let r=[],n=new z.MathNode("mtd",[],["mtr-glue"]),s=new z.MathNode("mtd",[],["mml-eqn-num"]);for(let y=0;y<t.body.length;y++){let w=t.body[y],S=[];for(let D=0;D<w.length;D++)S.push(new z.MathNode("mtd",[Z(w[D],e)]));t.tags&&t.tags[y]&&(S.unshift(n),S.push(n),t.leqno?S.unshift(s):S.push(s)),r.push(new z.MathNode("mtr",S))}let a=new z.MathNode("mtable",r),o=t.arraystretch===.5?.1:.16+t.arraystretch-1+(t.addJot?.09:0);a.setAttribute("rowspacing",A(o));let u="",m="";if(t.cols&&t.cols.length>0){let y=t.cols,w="",S=!1,D=0,q=y.length;y[0].type==="separator"&&(u+="top ",D=1),y[y.length-1].type==="separator"&&(u+="bottom ",q-=1);for(let V=D;V<q;V++)y[V].type==="align"?(m+=B1[y[V].align],S&&(w+="none "),S=!0):y[V].type==="separator"&&S&&(w+=y[V].separator==="|"?"solid ":"dashed ",S=!1);a.setAttribute("columnalign",m.trim()),/[sd]/.test(w)&&a.setAttribute("columnlines",w.trim())}if(t.colSeparationType==="align"){let y=t.cols||[],w="";for(let S=1;S<y.length;S++)w+=S%2?"0em ":"1em ";a.setAttribute("columnspacing",w.trim())}else t.colSeparationType==="alignat"||t.colSeparationType==="gather"?a.setAttribute("columnspacing","0em"):t.colSeparationType==="small"?a.setAttribute("columnspacing","0.2778em"):t.colSeparationType==="CD"?a.setAttribute("columnspacing","0.5em"):a.setAttribute("columnspacing","1em");let f="",b=t.hLinesBeforeRow;u+=b[0].length>0?"left ":"",u+=b[b.length-1].length>0?"right ":"";for(let y=1;y<b.length-1;y++)f+=b[y].length===0?"none ":b[y][0]?"dashed ":"solid ";return/[sd]/.test(f)&&a.setAttribute("rowlines",f.trim()),u!==""&&(a=new z.MathNode("menclose",[a]),a.setAttribute("notation",u.trim())),t.arraystretch&&t.arraystretch<1&&(a=new z.MathNode("mstyle",[a]),a.setAttribute("scriptlevel","1")),a},wr=function(t,e){t.envName.indexOf("ed")===-1&&Oe(t);let r=[],n=t.envName.indexOf("at")>-1?"alignat":"align",s=t.envName==="split",a=Z0(t.parser,{cols:r,addJot:!0,autoTag:s?void 0:pt(t.envName),emptySingleRow:!0,colSeparationType:n,maxNumCols:s?2:void 0,leqno:t.parser.settings.leqno},"display"),o,u=0,m={type:"ordgroup",mode:t.mode,body:[]};if(e[0]&&e[0].type==="ordgroup"){let b="";for(let y=0;y<e[0].body.length;y++){let w=F(e[0].body[y],"textord");b+=w.text}o=Number(b),u=o*2}let f=!u;a.body.forEach(function(b){for(let y=1;y<b.length;y+=2){let w=F(b[y],"styling");F(w.body[0],"ordgroup").body.unshift(m)}if(f)u<b.length&&(u=b.length);else{let y=b.length/2;if(o<y)throw new v("Too many math in a row: "+("expected "+o+", but got "+y),b[0])}});for(let b=0;b<u;++b){let y="r",w=0;b%2===1?y="l":b>0&&f&&(w=1),r[b]={type:"align",align:y,pregap:w,postgap:0}}return a.colSeparationType=f?"align":"alignat",a};O0({type:"array",names:["array","darray"],props:{numArgs:1},handler(t,e){let s=(De(e[0])?[e[0]]:F(e[0],"ordgroup").body).map(function(o){let m=rt(o).text;if("lcr".indexOf(m)!==-1)return{type:"align",align:m};if(m==="|")return{type:"separator",separator:"|"};if(m===":")return{type:"separator",separator:":"};throw new v("Unknown column alignment: "+m,o)}),a={cols:s,hskipBeforeAndAfter:!0,maxNumCols:s.length};return Z0(t.parser,a,ft(t.envName))},htmlBuilder:H0,mathmlBuilder:L0}),O0({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(t){let e={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[t.envName.replace("*","")],r="c",n={hskipBeforeAndAfter:!1,cols:[{type:"align",align:r}]};if(t.envName.charAt(t.envName.length-1)==="*"){let o=t.parser;if(o.consumeSpaces(),o.fetch().text==="["){if(o.consume(),o.consumeSpaces(),r=o.fetch().text,"lcr".indexOf(r)===-1)throw new v("Expected l or c or r",o.nextToken);o.consume(),o.consumeSpaces(),o.expect("]"),o.consume(),n.cols=[{type:"align",align:r}]}}let s=Z0(t.parser,n,ft(t.envName)),a=Math.max(0,...s.body.map(o=>o.length));return s.cols=new Array(a).fill({type:"align",align:r}),e?{type:"leftright",mode:t.mode,body:[s],left:e[0],right:e[1],rightColor:void 0}:s},htmlBuilder:H0,mathmlBuilder:L0}),O0({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(t){let e={arraystretch:.5},r=Z0(t.parser,e,"script");return r.colSeparationType="small",r},htmlBuilder:H0,mathmlBuilder:L0}),O0({type:"array",names:["subarray"],props:{numArgs:1},handler(t,e){let s=(De(e[0])?[e[0]]:F(e[0],"ordgroup").body).map(function(o){let m=rt(o).text;if("lc".indexOf(m)!==-1)return{type:"align",align:m};throw new v("Unknown column alignment: "+m,o)});if(s.length>1)throw new v("{subarray} can contain only one column");let a={cols:s,hskipBeforeAndAfter:!1,arraystretch:.5};if(a=Z0(t.parser,a,"script"),a.body.length>0&&a.body[0].length>1)throw new v("{subarray} can contain only one column");return a},htmlBuilder:H0,mathmlBuilder:L0}),O0({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(t){let e={arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},r=Z0(t.parser,e,ft(t.envName));return{type:"leftright",mode:t.mode,body:[r],left:t.envName.indexOf("r")>-1?".":"\\{",right:t.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:H0,mathmlBuilder:L0}),O0({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:wr,htmlBuilder:H0,mathmlBuilder:L0}),O0({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(t){T.contains(["gather","gather*"],t.envName)&&Oe(t);let e={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:pt(t.envName),emptySingleRow:!0,leqno:t.parser.settings.leqno};return Z0(t.parser,e,"display")},htmlBuilder:H0,mathmlBuilder:L0}),O0({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:wr,htmlBuilder:H0,mathmlBuilder:L0}),O0({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(t){Oe(t);let e={autoTag:pt(t.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:t.parser.settings.leqno};return Z0(t.parser,e,"display")},htmlBuilder:H0,mathmlBuilder:L0}),O0({type:"array",names:["CD"],props:{numArgs:0},handler(t){return Oe(t),f1(t.parser)},htmlBuilder:H0,mathmlBuilder:L0}),h("\\nonumber","\\gdef\\@eqnsw{0}"),h("\\notag","\\nonumber"),B({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(t,e){throw new v(t.funcName+" valid only within array environment")}});var kr=br;B({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(t,e){let{parser:r,funcName:n}=t,s=e[0];if(s.type!=="ordgroup")throw new v("Invalid environment name",s);let a="";for(let o=0;o<s.body.length;++o)a+=F(s.body[o],"textord").text;if(n==="\\begin"){if(!kr.hasOwnProperty(a))throw new v("No such environment: "+a,s);let o=kr[a],{args:u,optArgs:m}=r.parseArguments("\\begin{"+a+"}",o),f={mode:r.mode,envName:a,parser:r},b=o.handler(f,u,m);r.expect("\\end",!1);let y=r.nextToken,w=F(r.parseFunction(),"environment");if(w.name!==a)throw new v("Mismatch: \\begin{"+a+"} matched by \\end{"+w.name+"}",y);return b}return{type:"environment",mode:r.mode,name:a,nameGroup:s}}});let vr=(t,e)=>{let r=t.font,n=e.withFont(r);return U(t.body,n)},Sr=(t,e)=>{let r=t.font,n=e.withFont(r);return Z(t.body,n)},Mr={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};B({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathsfit","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(t,e)=>{let{parser:r,funcName:n}=t,s=Ne(e[0]),a=n;return a in Mr&&(a=Mr[a]),{type:"font",mode:r.mode,font:a.slice(1),body:s}},htmlBuilder:vr,mathmlBuilder:Sr}),B({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(t,e)=>{let{parser:r}=t,n=e[0],s=T.isCharacterBox(n);return{type:"mclass",mode:r.mode,mclass:Ee(n),body:[{type:"font",mode:r.mode,font:"boldsymbol",body:n}],isCharacterBox:s}}}),B({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(t,e)=>{let{parser:r,funcName:n,breakOnTokenText:s}=t,{mode:a}=r,o=r.parseExpression(!0,s),u="math"+n.slice(1);return{type:"font",mode:a,font:u,body:{type:"ordgroup",mode:r.mode,body:o}}},htmlBuilder:vr,mathmlBuilder:Sr});let zr=(t,e)=>{let r=e;return t==="display"?r=r.id>=E.SCRIPT.id?r.text():E.DISPLAY:t==="text"&&r.size===E.DISPLAY.size?r=E.TEXT:t==="script"?r=E.SCRIPT:t==="scriptscript"&&(r=E.SCRIPTSCRIPT),r},gt=(t,e)=>{let r=zr(t.size,e.style),n=r.fracNum(),s=r.fracDen(),a;a=e.havingStyle(n);let o=U(t.numer,a,e);if(t.continued){let G=8.5/e.fontMetrics().ptPerEm,Y=3.5/e.fontMetrics().ptPerEm;o.height=o.height<G?G:o.height,o.depth=o.depth<Y?Y:o.depth}a=e.havingStyle(s);let u=U(t.denom,a,e),m,f,b;t.hasBarLine?(t.barSize?(f=n0(t.barSize,e),m=x.makeLineSpan("frac-line",e,f)):m=x.makeLineSpan("frac-line",e),f=m.height,b=m.height):(m=null,f=0,b=e.fontMetrics().defaultRuleThickness);let y,w,S;r.size===E.DISPLAY.size||t.size==="display"?(y=e.fontMetrics().num1,f>0?w=3*b:w=7*b,S=e.fontMetrics().denom1):(f>0?(y=e.fontMetrics().num2,w=b):(y=e.fontMetrics().num3,w=3*b),S=e.fontMetrics().denom2);let D;if(m){let G=e.fontMetrics().axisHeight;y-o.depth-(G+.5*f)<w&&(y+=w-(y-o.depth-(G+.5*f))),G-.5*f-(u.height-S)<w&&(S+=w-(G-.5*f-(u.height-S)));let Y=-(G-.5*f);D=x.makeVList({positionType:"individualShift",children:[{type:"elem",elem:u,shift:S},{type:"elem",elem:m,shift:Y},{type:"elem",elem:o,shift:-y}]},e)}else{let G=y-o.depth-(u.height-S);G<w&&(y+=.5*(w-G),S+=.5*(w-G)),D=x.makeVList({positionType:"individualShift",children:[{type:"elem",elem:u,shift:S},{type:"elem",elem:o,shift:-y}]},e)}a=e.havingStyle(r),D.height*=a.sizeMultiplier/e.sizeMultiplier,D.depth*=a.sizeMultiplier/e.sizeMultiplier;let q;r.size===E.DISPLAY.size?q=e.fontMetrics().delim1:r.size===E.SCRIPTSCRIPT.size?q=e.havingStyle(E.SCRIPT).fontMetrics().delim2:q=e.fontMetrics().delim2;let V,_;return t.leftDelim==null?V=me(e,["mopen"]):V=W0.customSizedDelim(t.leftDelim,q,!0,e.havingStyle(r),t.mode,["mopen"]),t.continued?_=x.makeSpan([]):t.rightDelim==null?_=me(e,["mclose"]):_=W0.customSizedDelim(t.rightDelim,q,!0,e.havingStyle(r),t.mode,["mclose"]),x.makeSpan(["mord"].concat(a.sizingClasses(e)),[V,x.makeSpan(["mfrac"],[D]),_],e)},bt=(t,e)=>{let r=new z.MathNode("mfrac",[Z(t.numer,e),Z(t.denom,e)]);if(!t.hasBarLine)r.setAttribute("linethickness","0px");else if(t.barSize){let s=n0(t.barSize,e);r.setAttribute("linethickness",A(s))}let n=zr(t.size,e.style);if(n.size!==e.style.size){r=new z.MathNode("mstyle",[r]);let s=n.size===E.DISPLAY.size?"true":"false";r.setAttribute("displaystyle",s),r.setAttribute("scriptlevel","0")}if(t.leftDelim!=null||t.rightDelim!=null){let s=[];if(t.leftDelim!=null){let a=new z.MathNode("mo",[new z.TextNode(t.leftDelim.replace("\\",""))]);a.setAttribute("fence","true"),s.push(a)}if(s.push(r),t.rightDelim!=null){let a=new z.MathNode("mo",[new z.TextNode(t.rightDelim.replace("\\",""))]);a.setAttribute("fence","true"),s.push(a)}return Qe(s)}return r};B({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(t,e)=>{let{parser:r,funcName:n}=t,s=e[0],a=e[1],o,u=null,m=null,f="auto";switch(n){case"\\dfrac":case"\\frac":case"\\tfrac":o=!0;break;case"\\\\atopfrac":o=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":o=!1,u="(",m=")";break;case"\\\\bracefrac":o=!1,u="\\{",m="\\}";break;case"\\\\brackfrac":o=!1,u="[",m="]";break;default:throw new Error("Unrecognized genfrac command")}switch(n){case"\\dfrac":case"\\dbinom":f="display";break;case"\\tfrac":case"\\tbinom":f="text";break}return{type:"genfrac",mode:r.mode,continued:!1,numer:s,denom:a,hasBarLine:o,leftDelim:u,rightDelim:m,size:f,barSize:null}},htmlBuilder:gt,mathmlBuilder:bt}),B({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(t,e)=>{let{parser:r,funcName:n}=t,s=e[0],a=e[1];return{type:"genfrac",mode:r.mode,continued:!0,numer:s,denom:a,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),B({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(t){let{parser:e,funcName:r,token:n}=t,s;switch(r){case"\\over":s="\\frac";break;case"\\choose":s="\\binom";break;case"\\atop":s="\\\\atopfrac";break;case"\\brace":s="\\\\bracefrac";break;case"\\brack":s="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:e.mode,replaceWith:s,token:n}}});let Ar=["display","text","script","scriptscript"],Tr=function(t){let e=null;return t.length>0&&(e=t,e=e==="."?null:e),e};B({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(t,e){let{parser:r}=t,n=e[4],s=e[5],a=Ne(e[0]),o=a.type==="atom"&&a.family==="open"?Tr(a.text):null,u=Ne(e[1]),m=u.type==="atom"&&u.family==="close"?Tr(u.text):null,f=F(e[2],"size"),b,y=null;f.isBlank?b=!0:(y=f.value,b=y.number>0);let w="auto",S=e[3];if(S.type==="ordgroup"){if(S.body.length>0){let D=F(S.body[0],"textord");w=Ar[Number(D.text)]}}else S=F(S,"textord"),w=Ar[Number(S.text)];return{type:"genfrac",mode:r.mode,numer:n,denom:s,continued:!1,hasBarLine:b,barSize:y,leftDelim:o,rightDelim:m,size:w}},htmlBuilder:gt,mathmlBuilder:bt}),B({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(t,e){let{parser:r,funcName:n,token:s}=t;return{type:"infix",mode:r.mode,replaceWith:"\\\\abovefrac",size:F(e[0],"size").value,token:s}}}),B({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(t,e)=>{let{parser:r,funcName:n}=t,s=e[0],a=j(F(e[1],"infix").size),o=e[2],u=a.number>0;return{type:"genfrac",mode:r.mode,numer:s,denom:o,continued:!1,hasBarLine:u,barSize:a,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:gt,mathmlBuilder:bt});let Br=(t,e)=>{let r=e.style,n,s;t.type==="supsub"?(n=t.sup?U(t.sup,e.havingStyle(r.sup()),e):U(t.sub,e.havingStyle(r.sub()),e),s=F(t.base,"horizBrace")):s=F(t,"horizBrace");let a=U(s.base,e.havingBaseStyle(E.DISPLAY)),o=U0.svgSpan(s,e),u;if(s.isOver?(u=x.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:a},{type:"kern",size:.1},{type:"elem",elem:o}]},e),u.children[0].children[0].children[1].classes.push("svg-align")):(u=x.makeVList({positionType:"bottom",positionData:a.depth+.1+o.height,children:[{type:"elem",elem:o},{type:"kern",size:.1},{type:"elem",elem:a}]},e),u.children[0].children[0].children[0].classes.push("svg-align")),n){let m=x.makeSpan(["mord",s.isOver?"mover":"munder"],[u],e);s.isOver?u=x.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:m},{type:"kern",size:.2},{type:"elem",elem:n}]},e):u=x.makeVList({positionType:"bottom",positionData:m.depth+.2+n.height+n.depth,children:[{type:"elem",elem:n},{type:"kern",size:.2},{type:"elem",elem:m}]},e)}return x.makeSpan(["mord",s.isOver?"mover":"munder"],[u],e)};B({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(t,e){let{parser:r,funcName:n}=t;return{type:"horizBrace",mode:r.mode,label:n,isOver:/^\\over/.test(n),base:e[0]}},htmlBuilder:Br,mathmlBuilder:(t,e)=>{let r=U0.mathMLnode(t.label);return new z.MathNode(t.isOver?"mover":"munder",[Z(t.base,e),r])}}),B({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(t,e)=>{let{parser:r}=t,n=e[1],s=F(e[0],"url").url;return r.settings.isTrusted({command:"\\href",url:s})?{type:"href",mode:r.mode,href:s,body:u0(n)}:r.formatUnsupportedCmd("\\href")},htmlBuilder:(t,e)=>{let r=d0(t.body,e,!1);return x.makeAnchor(t.href,[],r,e)},mathmlBuilder:(t,e)=>{let r=j0(t.body,e);return r instanceof S0||(r=new S0("mrow",[r])),r.setAttribute("href",t.href),r}}),B({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(t,e)=>{let{parser:r}=t,n=F(e[0],"url").url;if(!r.settings.isTrusted({command:"\\url",url:n}))return r.formatUnsupportedCmd("\\url");let s=[];for(let o=0;o<n.length;o++){let u=n[o];u==="~"&&(u="\\textasciitilde"),s.push({type:"textord",mode:"text",text:u})}let a={type:"text",mode:r.mode,font:"\\texttt",body:s};return{type:"href",mode:r.mode,href:n,body:u0(a)}}}),B({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(t,e){let{parser:r}=t;return{type:"hbox",mode:r.mode,body:u0(e[0])}},htmlBuilder(t,e){let r=d0(t.body,e,!1);return x.makeFragment(r)},mathmlBuilder(t,e){return new z.MathNode("mrow",w0(t.body,e))}}),B({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(t,e)=>{let{parser:r,funcName:n,token:s}=t,a=F(e[0],"raw").string,o=e[1];r.settings.strict&&r.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");let u,m={};switch(n){case"\\htmlClass":m.class=a,u={command:"\\htmlClass",class:a};break;case"\\htmlId":m.id=a,u={command:"\\htmlId",id:a};break;case"\\htmlStyle":m.style=a,u={command:"\\htmlStyle",style:a};break;case"\\htmlData":{let f=a.split(",");for(let b=0;b<f.length;b++){let y=f[b].split("=");if(y.length!==2)throw new v("Error parsing key-value for \\htmlData");m["data-"+y[0].trim()]=y[1].trim()}u={command:"\\htmlData",attributes:m};break}default:throw new Error("Unrecognized html command")}return r.settings.isTrusted(u)?{type:"html",mode:r.mode,attributes:m,body:u0(o)}:r.formatUnsupportedCmd(n)},htmlBuilder:(t,e)=>{let r=d0(t.body,e,!1),n=["enclosing"];t.attributes.class&&n.push(...t.attributes.class.trim().split(/\s+/));let s=x.makeSpan(n,r,e);for(let a in t.attributes)a!=="class"&&t.attributes.hasOwnProperty(a)&&s.setAttribute(a,t.attributes[a]);return s},mathmlBuilder:(t,e)=>j0(t.body,e)}),B({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(t,e)=>{let{parser:r}=t;return{type:"htmlmathml",mode:r.mode,html:u0(e[0]),mathml:u0(e[1])}},htmlBuilder:(t,e)=>{let r=d0(t.html,e,!1);return x.makeFragment(r)},mathmlBuilder:(t,e)=>j0(t.mathml,e)});let yt=function(t){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(t))return{number:+t,unit:"bp"};{let e=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t);if(!e)throw new v("Invalid size: '"+t+"' in \\includegraphics");let r={number:+(e[1]+e[2]),unit:e[3]};if(!Et(r))throw new v("Invalid unit: '"+r.unit+"' in \\includegraphics.");return r}};B({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(t,e,r)=>{let{parser:n}=t,s={number:0,unit:"em"},a={number:.9,unit:"em"},o={number:0,unit:"em"},u="";if(r[0]){let b=F(r[0],"raw").string.split(",");for(let y=0;y<b.length;y++){let w=b[y].split("=");if(w.length===2){let S=w[1].trim();switch(w[0].trim()){case"alt":u=S;break;case"width":s=yt(S);break;case"height":a=yt(S);break;case"totalheight":o=yt(S);break;default:throw new v("Invalid key: '"+w[0]+"' in \\includegraphics.")}}}}let m=F(e[0],"url").url;return u===""&&(u=m,u=u.replace(/^.*[\\/]/,""),u=u.substring(0,u.lastIndexOf("."))),n.settings.isTrusted({command:"\\includegraphics",url:m})?{type:"includegraphics",mode:n.mode,alt:u,width:s,height:a,totalheight:o,src:m}:n.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(t,e)=>{let r=n0(t.height,e),n=0;t.totalheight.number>0&&(n=n0(t.totalheight,e)-r);let s=0;t.width.number>0&&(s=n0(t.width,e));let a={height:A(r+n)};s>0&&(a.width=A(s)),n>0&&(a.verticalAlign=A(-n));let o=new In(t.src,t.alt,a);return o.height=r,o.depth=n,o},mathmlBuilder:(t,e)=>{let r=new z.MathNode("mglyph",[]);r.setAttribute("alt",t.alt);let n=n0(t.height,e),s=0;if(t.totalheight.number>0&&(s=n0(t.totalheight,e)-n,r.setAttribute("valign",A(-s))),r.setAttribute("height",A(n+s)),t.width.number>0){let a=n0(t.width,e);r.setAttribute("width",A(a))}return r.setAttribute("src",t.src),r}}),B({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(t,e){let{parser:r,funcName:n}=t,s=F(e[0],"size");if(r.settings.strict){let a=n[1]==="m",o=s.value.unit==="mu";a?(o||r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" supports only mu units, "+("not "+s.value.unit+" units")),r.mode!=="math"&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" works only in math mode")):o&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" doesn't support mu units")}return{type:"kern",mode:r.mode,dimension:s.value}},htmlBuilder(t,e){return x.makeGlue(t.dimension,e)},mathmlBuilder(t,e){let r=n0(t.dimension,e);return new z.SpaceNode(r)}}),B({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{let{parser:r,funcName:n}=t,s=e[0];return{type:"lap",mode:r.mode,alignment:n.slice(5),body:s}},htmlBuilder:(t,e)=>{let r;t.alignment==="clap"?(r=x.makeSpan([],[U(t.body,e)]),r=x.makeSpan(["inner"],[r],e)):r=x.makeSpan(["inner"],[U(t.body,e)]);let n=x.makeSpan(["fix"],[]),s=x.makeSpan([t.alignment],[r,n],e),a=x.makeSpan(["strut"]);return a.style.height=A(s.height+s.depth),s.depth&&(a.style.verticalAlign=A(-s.depth)),s.children.unshift(a),s=x.makeSpan(["thinbox"],[s],e),x.makeSpan(["mord","vbox"],[s],e)},mathmlBuilder:(t,e)=>{let r=new z.MathNode("mpadded",[Z(t.body,e)]);if(t.alignment!=="rlap"){let n=t.alignment==="llap"?"-1":"-0.5";r.setAttribute("lspace",n+"width")}return r.setAttribute("width","0px"),r}}),B({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(t,e){let{funcName:r,parser:n}=t,s=n.mode;n.switchMode("math");let a=r==="\\("?"\\)":"$",o=n.parseExpression(!1,a);return n.expect(a),n.switchMode(s),{type:"styling",mode:n.mode,style:"text",body:o}}}),B({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(t,e){throw new v("Mismatched "+t.funcName)}});let Nr=(t,e)=>{switch(e.style.size){case E.DISPLAY.size:return t.display;case E.TEXT.size:return t.text;case E.SCRIPT.size:return t.script;case E.SCRIPTSCRIPT.size:return t.scriptscript;default:return t.text}};B({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(t,e)=>{let{parser:r}=t;return{type:"mathchoice",mode:r.mode,display:u0(e[0]),text:u0(e[1]),script:u0(e[2]),scriptscript:u0(e[3])}},htmlBuilder:(t,e)=>{let r=Nr(t,e),n=d0(r,e,!1);return x.makeFragment(n)},mathmlBuilder:(t,e)=>{let r=Nr(t,e);return j0(r,e)}});let Cr=(t,e,r,n,s,a,o)=>{t=x.makeSpan([],[t]);let u=r&&T.isCharacterBox(r),m,f;if(e){let w=U(e,n.havingStyle(s.sup()),n);f={elem:w,kern:Math.max(n.fontMetrics().bigOpSpacing1,n.fontMetrics().bigOpSpacing3-w.depth)}}if(r){let w=U(r,n.havingStyle(s.sub()),n);m={elem:w,kern:Math.max(n.fontMetrics().bigOpSpacing2,n.fontMetrics().bigOpSpacing4-w.height)}}let b;if(f&&m){let w=n.fontMetrics().bigOpSpacing5+m.elem.height+m.elem.depth+m.kern+t.depth+o;b=x.makeVList({positionType:"bottom",positionData:w,children:[{type:"kern",size:n.fontMetrics().bigOpSpacing5},{type:"elem",elem:m.elem,marginLeft:A(-a)},{type:"kern",size:m.kern},{type:"elem",elem:t},{type:"kern",size:f.kern},{type:"elem",elem:f.elem,marginLeft:A(a)},{type:"kern",size:n.fontMetrics().bigOpSpacing5}]},n)}else if(m){let w=t.height-o;b=x.makeVList({positionType:"top",positionData:w,children:[{type:"kern",size:n.fontMetrics().bigOpSpacing5},{type:"elem",elem:m.elem,marginLeft:A(-a)},{type:"kern",size:m.kern},{type:"elem",elem:t}]},n)}else if(f){let w=t.depth+o;b=x.makeVList({positionType:"bottom",positionData:w,children:[{type:"elem",elem:t},{type:"kern",size:f.kern},{type:"elem",elem:f.elem,marginLeft:A(a)},{type:"kern",size:n.fontMetrics().bigOpSpacing5}]},n)}else return t;let y=[b];if(m&&a!==0&&!u){let w=x.makeSpan(["mspace"],[],n);w.style.marginRight=A(a),y.unshift(w)}return x.makeSpan(["mop","op-limits"],y,n)},Dr=["\\smallint"],ie=(t,e)=>{let r,n,s=!1,a;t.type==="supsub"?(r=t.sup,n=t.sub,a=F(t.base,"op"),s=!0):a=F(t,"op");let o=e.style,u=!1;o.size===E.DISPLAY.size&&a.symbol&&!T.contains(Dr,a.name)&&(u=!0);let m;if(a.symbol){let y=u?"Size2-Regular":"Size1-Regular",w="";if((a.name==="\\oiint"||a.name==="\\oiiint")&&(w=a.name.slice(1),a.name=w==="oiint"?"\\iint":"\\iiint"),m=x.makeSymbol(a.name,y,"math",e,["mop","op-symbol",u?"large-op":"small-op"]),w.length>0){let S=m.italic,D=x.staticSvg(w+"Size"+(u?"2":"1"),e);m=x.makeVList({positionType:"individualShift",children:[{type:"elem",elem:m,shift:0},{type:"elem",elem:D,shift:u?.08:0}]},e),a.name="\\"+w,m.classes.unshift("mop"),m.italic=S}}else if(a.body){let y=d0(a.body,e,!0);y.length===1&&y[0]instanceof A0?(m=y[0],m.classes[0]="mop"):m=x.makeSpan(["mop"],y,e)}else{let y=[];for(let w=1;w<a.name.length;w++)y.push(x.mathsym(a.name[w],a.mode,e));m=x.makeSpan(["mop"],y,e)}let f=0,b=0;return(m instanceof A0||a.name==="\\oiint"||a.name==="\\oiiint")&&!a.suppressBaseShift&&(f=(m.height-m.depth)/2-e.fontMetrics().axisHeight,b=m.italic),s?Cr(m,r,n,e,o,b,f):(f&&(m.style.position="relative",m.style.top=A(f)),m)},fe=(t,e)=>{let r;if(t.symbol)r=new S0("mo",[T0(t.name,t.mode)]),T.contains(Dr,t.name)&&r.setAttribute("largeop","false");else if(t.body)r=new S0("mo",w0(t.body,e));else{r=new S0("mi",[new R0(t.name.slice(1))]);let n=new S0("mo",[T0("\u2061","text")]);t.parentIsSupSub?r=new S0("mrow",[r,n]):r=Xt([r,n])}return r},N1={"\u220F":"\\prod","\u2210":"\\coprod","\u2211":"\\sum","\u22C0":"\\bigwedge","\u22C1":"\\bigvee","\u22C2":"\\bigcap","\u22C3":"\\bigcup","\u2A00":"\\bigodot","\u2A01":"\\bigoplus","\u2A02":"\\bigotimes","\u2A04":"\\biguplus","\u2A06":"\\bigsqcup"};B({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","\u220F","\u2210","\u2211","\u22C0","\u22C1","\u22C2","\u22C3","\u2A00","\u2A01","\u2A02","\u2A04","\u2A06"],props:{numArgs:0},handler:(t,e)=>{let{parser:r,funcName:n}=t,s=n;return s.length===1&&(s=N1[s]),{type:"op",mode:r.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:s}},htmlBuilder:ie,mathmlBuilder:fe}),B({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(t,e)=>{let{parser:r}=t,n=e[0];return{type:"op",mode:r.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:u0(n)}},htmlBuilder:ie,mathmlBuilder:fe});let C1={"\u222B":"\\int","\u222C":"\\iint","\u222D":"\\iiint","\u222E":"\\oint","\u222F":"\\oiint","\u2230":"\\oiiint"};B({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(t){let{parser:e,funcName:r}=t;return{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:ie,mathmlBuilder:fe}),B({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(t){let{parser:e,funcName:r}=t;return{type:"op",mode:e.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:ie,mathmlBuilder:fe}),B({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","\u222B","\u222C","\u222D","\u222E","\u222F","\u2230"],props:{numArgs:0},handler(t){let{parser:e,funcName:r}=t,n=r;return n.length===1&&(n=C1[n]),{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:n}},htmlBuilder:ie,mathmlBuilder:fe});let qr=(t,e)=>{let r,n,s=!1,a;t.type==="supsub"?(r=t.sup,n=t.sub,a=F(t.base,"operatorname"),s=!0):a=F(t,"operatorname");let o;if(a.body.length>0){let u=a.body.map(f=>{let b=f.text;return typeof b=="string"?{type:"textord",mode:f.mode,text:b}:f}),m=d0(u,e.withFont("mathrm"),!0);for(let f=0;f<m.length;f++){let b=m[f];b instanceof A0&&(b.text=b.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}o=x.makeSpan(["mop"],m,e)}else o=x.makeSpan(["mop"],[],e);return s?Cr(o,r,n,e,e.style,0,0):o};B({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(t,e)=>{let{parser:r,funcName:n}=t,s=e[0];return{type:"operatorname",mode:r.mode,body:u0(s),alwaysHandleSupSub:n==="\\operatornamewithlimits",limits:!1,parentIsSupSub:!1}},htmlBuilder:qr,mathmlBuilder:(t,e)=>{let r=w0(t.body,e.withFont("mathrm")),n=!0;for(let o=0;o<r.length;o++){let u=r[o];if(!(u instanceof z.SpaceNode))if(u instanceof z.MathNode)switch(u.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{let m=u.children[0];u.children.length===1&&m instanceof z.TextNode?m.text=m.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):n=!1;break}default:n=!1}else n=!1}if(n){let o=r.map(u=>u.toText()).join("");r=[new z.TextNode(o)]}let s=new z.MathNode("mi",r);s.setAttribute("mathvariant","normal");let a=new z.MathNode("mo",[T0("\u2061","text")]);return t.parentIsSupSub?new z.MathNode("mrow",[s,a]):z.newDocumentFragment([s,a])}}),h("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@"),Q0({type:"ordgroup",htmlBuilder(t,e){return t.semisimple?x.makeFragment(d0(t.body,e,!1)):x.makeSpan(["mord"],d0(t.body,e,!0),e)},mathmlBuilder(t,e){return j0(t.body,e,!0)}}),B({type:"overline",names:["\\overline"],props:{numArgs:1},handler(t,e){let{parser:r}=t,n=e[0];return{type:"overline",mode:r.mode,body:n}},htmlBuilder(t,e){let r=U(t.body,e.havingCrampedStyle()),n=x.makeLineSpan("overline-line",e),s=e.fontMetrics().defaultRuleThickness,a=x.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:3*s},{type:"elem",elem:n},{type:"kern",size:s}]},e);return x.makeSpan(["mord","overline"],[a],e)},mathmlBuilder(t,e){let r=new z.MathNode("mo",[new z.TextNode("\u203E")]);r.setAttribute("stretchy","true");let n=new z.MathNode("mover",[Z(t.body,e),r]);return n.setAttribute("accent","true"),n}}),B({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{let{parser:r}=t,n=e[0];return{type:"phantom",mode:r.mode,body:u0(n)}},htmlBuilder:(t,e)=>{let r=d0(t.body,e.withPhantom(),!1);return x.makeFragment(r)},mathmlBuilder:(t,e)=>{let r=w0(t.body,e);return new z.MathNode("mphantom",r)}}),B({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{let{parser:r}=t,n=e[0];return{type:"hphantom",mode:r.mode,body:n}},htmlBuilder:(t,e)=>{let r=x.makeSpan([],[U(t.body,e.withPhantom())]);if(r.height=0,r.depth=0,r.children)for(let n=0;n<r.children.length;n++)r.children[n].height=0,r.children[n].depth=0;return r=x.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},e),x.makeSpan(["mord"],[r],e)},mathmlBuilder:(t,e)=>{let r=w0(u0(t.body),e),n=new z.MathNode("mphantom",r),s=new z.MathNode("mpadded",[n]);return s.setAttribute("height","0px"),s.setAttribute("depth","0px"),s}}),B({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(t,e)=>{let{parser:r}=t,n=e[0];return{type:"vphantom",mode:r.mode,body:n}},htmlBuilder:(t,e)=>{let r=x.makeSpan(["inner"],[U(t.body,e.withPhantom())]),n=x.makeSpan(["fix"],[]);return x.makeSpan(["mord","rlap"],[r,n],e)},mathmlBuilder:(t,e)=>{let r=w0(u0(t.body),e),n=new z.MathNode("mphantom",r),s=new z.MathNode("mpadded",[n]);return s.setAttribute("width","0px"),s}}),B({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(t,e){let{parser:r}=t,n=F(e[0],"size").value,s=e[1];return{type:"raisebox",mode:r.mode,dy:n,body:s}},htmlBuilder(t,e){let r=U(t.body,e),n=n0(t.dy,e);return x.makeVList({positionType:"shift",positionData:-n,children:[{type:"elem",elem:r}]},e)},mathmlBuilder(t,e){let r=new z.MathNode("mpadded",[Z(t.body,e)]),n=t.dy.number+t.dy.unit;return r.setAttribute("voffset",n),r}}),B({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0},handler(t){let{parser:e}=t;return{type:"internal",mode:e.mode}}}),B({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["size","size","size"]},handler(t,e,r){let{parser:n}=t,s=r[0],a=F(e[0],"size"),o=F(e[1],"size");return{type:"rule",mode:n.mode,shift:s&&F(s,"size").value,width:a.value,height:o.value}},htmlBuilder(t,e){let r=x.makeSpan(["mord","rule"],[],e),n=n0(t.width,e),s=n0(t.height,e),a=t.shift?n0(t.shift,e):0;return r.style.borderRightWidth=A(n),r.style.borderTopWidth=A(s),r.style.bottom=A(a),r.width=n,r.height=s+a,r.depth=-a,r.maxFontSize=s*1.125*e.sizeMultiplier,r},mathmlBuilder(t,e){let r=n0(t.width,e),n=n0(t.height,e),s=t.shift?n0(t.shift,e):0,a=e.color&&e.getColor()||"black",o=new z.MathNode("mspace");o.setAttribute("mathbackground",a),o.setAttribute("width",A(r)),o.setAttribute("height",A(n));let u=new z.MathNode("mpadded",[o]);return s>=0?u.setAttribute("height",A(s)):(u.setAttribute("height",A(s)),u.setAttribute("depth",A(-s))),u.setAttribute("voffset",A(s)),u}});function Er(t,e,r){let n=d0(t,e,!1),s=e.sizeMultiplier/r.sizeMultiplier;for(let a=0;a<n.length;a++){let o=n[a].classes.indexOf("sizing");o<0?Array.prototype.push.apply(n[a].classes,e.sizingClasses(r)):n[a].classes[o+1]==="reset-size"+e.size&&(n[a].classes[o+1]="reset-size"+r.size),n[a].height*=s,n[a].depth*=s}return x.makeFragment(n)}let Ir=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"];B({type:"sizing",names:Ir,props:{numArgs:0,allowedInText:!0},handler:(t,e)=>{let{breakOnTokenText:r,funcName:n,parser:s}=t,a=s.parseExpression(!1,r);return{type:"sizing",mode:s.mode,size:Ir.indexOf(n)+1,body:a}},htmlBuilder:(t,e)=>{let r=e.havingSize(t.size);return Er(t.body,r,e)},mathmlBuilder:(t,e)=>{let r=e.havingSize(t.size),n=w0(t.body,r),s=new z.MathNode("mstyle",n);return s.setAttribute("mathsize",A(r.sizeMultiplier)),s}}),B({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(t,e,r)=>{let{parser:n}=t,s=!1,a=!1,o=r[0]&&F(r[0],"ordgroup");if(o){let m="";for(let f=0;f<o.body.length;++f)if(m=o.body[f].text,m==="t")s=!0;else if(m==="b")a=!0;else{s=!1,a=!1;break}}else s=!0,a=!0;let u=e[0];return{type:"smash",mode:n.mode,body:u,smashHeight:s,smashDepth:a}},htmlBuilder:(t,e)=>{let r=x.makeSpan([],[U(t.body,e)]);if(!t.smashHeight&&!t.smashDepth)return r;if(t.smashHeight&&(r.height=0,r.children))for(let s=0;s<r.children.length;s++)r.children[s].height=0;if(t.smashDepth&&(r.depth=0,r.children))for(let s=0;s<r.children.length;s++)r.children[s].depth=0;let n=x.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},e);return x.makeSpan(["mord"],[n],e)},mathmlBuilder:(t,e)=>{let r=new z.MathNode("mpadded",[Z(t.body,e)]);return t.smashHeight&&r.setAttribute("height","0px"),t.smashDepth&&r.setAttribute("depth","0px"),r}}),B({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(t,e,r){let{parser:n}=t,s=r[0],a=e[0];return{type:"sqrt",mode:n.mode,body:a,index:s}},htmlBuilder(t,e){let r=U(t.body,e.havingCrampedStyle());r.height===0&&(r.height=e.fontMetrics().xHeight),r=x.wrapFragment(r,e);let s=e.fontMetrics().defaultRuleThickness,a=s;e.style.id<E.TEXT.id&&(a=e.fontMetrics().xHeight);let o=s+a/4,u=r.height+r.depth+o+s,{span:m,ruleWidth:f,advanceWidth:b}=W0.sqrtImage(u,e),y=m.height-f;y>r.height+r.depth+o&&(o=(o+y-r.height-r.depth)/2);let w=m.height-r.height-o-f;r.style.paddingLeft=A(b);let S=x.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:-(r.height+w)},{type:"elem",elem:m},{type:"kern",size:f}]},e);if(t.index){let D=e.havingStyle(E.SCRIPTSCRIPT),q=U(t.index,D,e),V=.6*(S.height-S.depth),_=x.makeVList({positionType:"shift",positionData:-V,children:[{type:"elem",elem:q}]},e),G=x.makeSpan(["root"],[_]);return x.makeSpan(["mord","sqrt"],[G,S],e)}else return x.makeSpan(["mord","sqrt"],[S],e)},mathmlBuilder(t,e){let{body:r,index:n}=t;return n?new z.MathNode("mroot",[Z(r,e),Z(n,e)]):new z.MathNode("msqrt",[Z(r,e)])}});let Rr={display:E.DISPLAY,text:E.TEXT,script:E.SCRIPT,scriptscript:E.SCRIPTSCRIPT};B({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(t,e){let{breakOnTokenText:r,funcName:n,parser:s}=t,a=s.parseExpression(!0,r),o=n.slice(1,n.length-5);return{type:"styling",mode:s.mode,style:o,body:a}},htmlBuilder(t,e){let r=Rr[t.style],n=e.havingStyle(r).withFont("");return Er(t.body,n,e)},mathmlBuilder(t,e){let r=Rr[t.style],n=e.havingStyle(r),s=w0(t.body,n),a=new z.MathNode("mstyle",s),u={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[t.style];return a.setAttribute("scriptlevel",u[0]),a.setAttribute("displaystyle",u[1]),a}});let D1=function(t,e){let r=t.base;return r?r.type==="op"?r.limits&&(e.style.size===E.DISPLAY.size||r.alwaysHandleSupSub)?ie:null:r.type==="operatorname"?r.alwaysHandleSupSub&&(e.style.size===E.DISPLAY.size||r.limits)?qr:null:r.type==="accent"?T.isCharacterBox(r.base)?nt:null:r.type==="horizBrace"&&!t.sub===r.isOver?Br:null:null};Q0({type:"supsub",htmlBuilder(t,e){let r=D1(t,e);if(r)return r(t,e);let{base:n,sup:s,sub:a}=t,o=U(n,e),u,m,f=e.fontMetrics(),b=0,y=0,w=n&&T.isCharacterBox(n);if(s){let Y=e.havingStyle(e.style.sup());u=U(s,Y,e),w||(b=o.height-Y.fontMetrics().supDrop*Y.sizeMultiplier/e.sizeMultiplier)}if(a){let Y=e.havingStyle(e.style.sub());m=U(a,Y,e),w||(y=o.depth+Y.fontMetrics().subDrop*Y.sizeMultiplier/e.sizeMultiplier)}let S;e.style===E.DISPLAY?S=f.sup1:e.style.cramped?S=f.sup3:S=f.sup2;let D=e.sizeMultiplier,q=A(.5/f.ptPerEm/D),V=null;if(m){let Y=t.base&&t.base.type==="op"&&t.base.name&&(t.base.name==="\\oiint"||t.base.name==="\\oiiint");(o instanceof A0||Y)&&(V=A(-o.italic))}let _;if(u&&m){b=Math.max(b,S,u.depth+.25*f.xHeight),y=Math.max(y,f.sub2);let K=4*f.defaultRuleThickness;if(b-u.depth-(m.height-y)<K){y=K-(b-u.depth)+m.height;let p0=.8*f.xHeight-(b-u.depth);p0>0&&(b+=p0,y-=p0)}let y0=[{type:"elem",elem:m,shift:y,marginRight:q,marginLeft:V},{type:"elem",elem:u,shift:-b,marginRight:q}];_=x.makeVList({positionType:"individualShift",children:y0},e)}else if(m){y=Math.max(y,f.sub1,m.height-.8*f.xHeight);let Y=[{type:"elem",elem:m,marginLeft:V,marginRight:q}];_=x.makeVList({positionType:"shift",positionData:y,children:Y},e)}else if(u)b=Math.max(b,S,u.depth+.25*f.xHeight),_=x.makeVList({positionType:"shift",positionData:-b,children:[{type:"elem",elem:u,marginRight:q}]},e);else throw new Error("supsub must have either sup or sub.");let G=Ke(o,"right")||"mord";return x.makeSpan([G],[o,x.makeSpan(["msupsub"],[_])],e)},mathmlBuilder(t,e){let r=!1,n,s;t.base&&t.base.type==="horizBrace"&&(s=!!t.sup,s===t.base.isOver&&(r=!0,n=t.base.isOver)),t.base&&(t.base.type==="op"||t.base.type==="operatorname")&&(t.base.parentIsSupSub=!0);let a=[Z(t.base,e)];t.sub&&a.push(Z(t.sub,e)),t.sup&&a.push(Z(t.sup,e));let o;if(r)o=n?"mover":"munder";else if(t.sub)if(t.sup){let u=t.base;u&&u.type==="op"&&u.limits&&e.style===E.DISPLAY||u&&u.type==="operatorname"&&u.alwaysHandleSupSub&&(e.style===E.DISPLAY||u.limits)?o="munderover":o="msubsup"}else{let u=t.base;u&&u.type==="op"&&u.limits&&(e.style===E.DISPLAY||u.alwaysHandleSupSub)||u&&u.type==="operatorname"&&u.alwaysHandleSupSub&&(u.limits||e.style===E.DISPLAY)?o="munder":o="msub"}else{let u=t.base;u&&u.type==="op"&&u.limits&&(e.style===E.DISPLAY||u.alwaysHandleSupSub)||u&&u.type==="operatorname"&&u.alwaysHandleSupSub&&(u.limits||e.style===E.DISPLAY)?o="mover":o="msup"}return new z.MathNode(o,a)}}),Q0({type:"atom",htmlBuilder(t,e){return x.mathsym(t.text,t.mode,e,["m"+t.family])},mathmlBuilder(t,e){let r=new z.MathNode("mo",[T0(t.text,t.mode)]);if(t.family==="bin"){let n=et(t,e);n==="bold-italic"&&r.setAttribute("mathvariant",n)}else t.family==="punct"?r.setAttribute("separator","true"):(t.family==="open"||t.family==="close")&&r.setAttribute("stretchy","false");return r}});let Or={mi:"italic",mn:"normal",mtext:"normal"};Q0({type:"mathord",htmlBuilder(t,e){return x.makeOrd(t,e,"mathord")},mathmlBuilder(t,e){let r=new z.MathNode("mi",[T0(t.text,t.mode,e)]),n=et(t,e)||"italic";return n!==Or[r.type]&&r.setAttribute("mathvariant",n),r}}),Q0({type:"textord",htmlBuilder(t,e){return x.makeOrd(t,e,"textord")},mathmlBuilder(t,e){let r=T0(t.text,t.mode,e),n=et(t,e)||"normal",s;return t.mode==="text"?s=new z.MathNode("mtext",[r]):/[0-9]/.test(t.text)?s=new z.MathNode("mn",[r]):t.text==="\\prime"?s=new z.MathNode("mo",[r]):s=new z.MathNode("mi",[r]),n!==Or[s.type]&&s.setAttribute("mathvariant",n),s}});let xt={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},wt={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};Q0({type:"spacing",htmlBuilder(t,e){if(wt.hasOwnProperty(t.text)){let r=wt[t.text].className||"";if(t.mode==="text"){let n=x.makeOrd(t,e,"textord");return n.classes.push(r),n}else return x.makeSpan(["mspace",r],[x.mathsym(t.text,t.mode,e)],e)}else{if(xt.hasOwnProperty(t.text))return x.makeSpan(["mspace",xt[t.text]],[],e);throw new v('Unknown type of space "'+t.text+'"')}},mathmlBuilder(t,e){let r;if(wt.hasOwnProperty(t.text))r=new z.MathNode("mtext",[new z.TextNode("\xA0")]);else{if(xt.hasOwnProperty(t.text))return new z.MathNode("mspace");throw new v('Unknown type of space "'+t.text+'"')}return r}});let Hr=()=>{let t=new z.MathNode("mtd",[]);return t.setAttribute("width","50%"),t};Q0({type:"tag",mathmlBuilder(t,e){let r=new z.MathNode("mtable",[new z.MathNode("mtr",[Hr(),new z.MathNode("mtd",[j0(t.body,e)]),Hr(),new z.MathNode("mtd",[j0(t.tag,e)])])]);return r.setAttribute("width","100%"),r}});let Lr={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},Fr={"\\textbf":"textbf","\\textmd":"textmd"},q1={"\\textit":"textit","\\textup":"textup"},_r=(t,e)=>{let r=t.font;if(r){if(Lr[r])return e.withTextFontFamily(Lr[r]);if(Fr[r])return e.withTextFontWeight(Fr[r]);if(r==="\\emph")return e.fontShape==="textit"?e.withTextFontShape("textup"):e.withTextFontShape("textit")}else return e;return e.withTextFontShape(q1[r])};B({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup","\\emph"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(t,e){let{parser:r,funcName:n}=t,s=e[0];return{type:"text",mode:r.mode,body:u0(s),font:n}},htmlBuilder(t,e){let r=_r(t,e),n=d0(t.body,r,!0);return x.makeSpan(["mord","text"],n,r)},mathmlBuilder(t,e){let r=_r(t,e);return j0(t.body,r)}}),B({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(t,e){let{parser:r}=t;return{type:"underline",mode:r.mode,body:e[0]}},htmlBuilder(t,e){let r=U(t.body,e),n=x.makeLineSpan("underline-line",e),s=e.fontMetrics().defaultRuleThickness,a=x.makeVList({positionType:"top",positionData:r.height,children:[{type:"kern",size:s},{type:"elem",elem:n},{type:"kern",size:3*s},{type:"elem",elem:r}]},e);return x.makeSpan(["mord","underline"],[a],e)},mathmlBuilder(t,e){let r=new z.MathNode("mo",[new z.TextNode("\u203E")]);r.setAttribute("stretchy","true");let n=new z.MathNode("munder",[Z(t.body,e),r]);return n.setAttribute("accentunder","true"),n}}),B({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(t,e){let{parser:r}=t;return{type:"vcenter",mode:r.mode,body:e[0]}},htmlBuilder(t,e){let r=U(t.body,e),n=e.fontMetrics().axisHeight,s=.5*(r.height-n-(r.depth+n));return x.makeVList({positionType:"shift",positionData:s,children:[{type:"elem",elem:r}]},e)},mathmlBuilder(t,e){return new z.MathNode("mpadded",[Z(t.body,e)],["vcenter"])}}),B({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(t,e,r){throw new v("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(t,e){let r=Pr(t),n=[],s=e.havingStyle(e.style.text());for(let a=0;a<r.length;a++){let o=r[a];o==="~"&&(o="\\textasciitilde"),n.push(x.makeSymbol(o,"Typewriter-Regular",t.mode,s,["mord","texttt"]))}return x.makeSpan(["mord","text"].concat(s.sizingClasses(e)),x.tryCombineChars(n),s)},mathmlBuilder(t,e){let r=new z.TextNode(Pr(t)),n=new z.MathNode("mtext",[r]);return n.setAttribute("mathvariant","monospace"),n}});let Pr=t=>t.body.replace(/ /g,t.star?"\u2423":"\xA0");var K0=Wt;let Vr=`[ \r
	]`,E1="\\\\[a-zA-Z@]+",I1="\\\\[^\uD800-\uDFFF]",R1="("+E1+")"+Vr+"*",O1=`\\\\(
|[ \r	]+
?)[ \r	]*`,kt="[\u0300-\u036F]",H1=new RegExp(kt+"+$"),L1="("+Vr+"+)|"+(O1+"|")+"([!-\\[\\]-\u2027\u202A-\uD7FF\uF900-\uFFFF]"+(kt+"*")+"|[\uD800-\uDBFF][\uDC00-\uDFFF]"+(kt+"*")+"|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5"+("|"+R1)+("|"+I1+")");class Gr{constructor(e,r){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=r,this.tokenRegex=new RegExp(L1,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,r){this.catcodes[e]=r}lex(){let e=this.input,r=this.tokenRegex.lastIndex;if(r===e.length)return new B0("EOF",new M0(this,r,r));let n=this.tokenRegex.exec(e);if(n===null||n.index!==r)throw new v("Unexpected character: '"+e[r]+"'",new B0(e[r],new M0(this,r,r+1)));let s=n[6]||n[3]||(n[2]?"\\ ":" ");if(this.catcodes[s]===14){let a=e.indexOf(`
`,this.tokenRegex.lastIndex);return a===-1?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=a+1,this.lex()}return new B0(s,new M0(this,r,this.tokenRegex.lastIndex))}}class F1{constructor(e,r){e===void 0&&(e={}),r===void 0&&(r={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=r,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(this.undefStack.length===0)throw new v("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");let e=this.undefStack.pop();for(let r in e)e.hasOwnProperty(r)&&(e[r]==null?delete this.current[r]:this.current[r]=e[r])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,r,n){if(n===void 0&&(n=!1),n){for(let s=0;s<this.undefStack.length;s++)delete this.undefStack[s][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=r)}else{let s=this.undefStack[this.undefStack.length-1];s&&!s.hasOwnProperty(e)&&(s[e]=this.current[e])}r==null?delete this.current[e]:this.current[e]=r}}var _1=yr;h("\\noexpand",function(t){let e=t.popToken();return t.isExpandable(e.text)&&(e.noexpand=!0,e.treatAsRelax=!0),{tokens:[e],numArgs:0}}),h("\\expandafter",function(t){let e=t.popToken();return t.expandOnce(!0),{tokens:[e],numArgs:0}}),h("\\@firstoftwo",function(t){return{tokens:t.consumeArgs(2)[0],numArgs:0}}),h("\\@secondoftwo",function(t){return{tokens:t.consumeArgs(2)[1],numArgs:0}}),h("\\@ifnextchar",function(t){let e=t.consumeArgs(3);t.consumeSpaces();let r=t.future();return e[0].length===1&&e[0][0].text===r.text?{tokens:e[1],numArgs:0}:{tokens:e[2],numArgs:0}}),h("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}"),h("\\TextOrMath",function(t){let e=t.consumeArgs(2);return t.mode==="text"?{tokens:e[0],numArgs:0}:{tokens:e[1],numArgs:0}});let $r={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};h("\\char",function(t){let e=t.popToken(),r,n="";if(e.text==="'")r=8,e=t.popToken();else if(e.text==='"')r=16,e=t.popToken();else if(e.text==="`")if(e=t.popToken(),e.text[0]==="\\")n=e.text.charCodeAt(1);else{if(e.text==="EOF")throw new v("\\char` missing argument");n=e.text.charCodeAt(0)}else r=10;if(r){if(n=$r[e.text],n==null||n>=r)throw new v("Invalid base-"+r+" digit "+e.text);let s;for(;(s=$r[t.future().text])!=null&&s<r;)n*=r,n+=s,t.popToken()}return"\\@char{"+n+"}"});let vt=(t,e,r,n)=>{let s=t.consumeArg().tokens;if(s.length!==1)throw new v("\\newcommand's first argument must be a macro name");let a=s[0].text,o=t.isDefined(a);if(o&&!e)throw new v("\\newcommand{"+a+"} attempting to redefine "+(a+"; use \\renewcommand"));if(!o&&!r)throw new v("\\renewcommand{"+a+"} when command "+a+" does not yet exist; use \\newcommand");let u=0;if(s=t.consumeArg().tokens,s.length===1&&s[0].text==="["){let m="",f=t.expandNextToken();for(;f.text!=="]"&&f.text!=="EOF";)m+=f.text,f=t.expandNextToken();if(!m.match(/^\s*[0-9]+\s*$/))throw new v("Invalid number of arguments: "+m);u=parseInt(m),s=t.consumeArg().tokens}return o&&n||t.macros.set(a,{tokens:s,numArgs:u}),""};h("\\newcommand",t=>vt(t,!1,!0,!1)),h("\\renewcommand",t=>vt(t,!0,!1,!1)),h("\\providecommand",t=>vt(t,!0,!0,!0)),h("\\message",t=>{let e=t.consumeArgs(1)[0];return console.log(e.reverse().map(r=>r.text).join("")),""}),h("\\errmessage",t=>{let e=t.consumeArgs(1)[0];return console.error(e.reverse().map(r=>r.text).join("")),""}),h("\\show",t=>{let e=t.popToken(),r=e.text;return console.log(e,t.macros.get(r),K0[r],s0.math[r],s0.text[r]),""}),h("\\bgroup","{"),h("\\egroup","}"),h("~","\\nobreakspace"),h("\\lq","`"),h("\\rq","'"),h("\\aa","\\r a"),h("\\AA","\\r A"),h("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`\xA9}"),h("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}"),h("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`\xAE}"),h("\u212C","\\mathscr{B}"),h("\u2130","\\mathscr{E}"),h("\u2131","\\mathscr{F}"),h("\u210B","\\mathscr{H}"),h("\u2110","\\mathscr{I}"),h("\u2112","\\mathscr{L}"),h("\u2133","\\mathscr{M}"),h("\u211B","\\mathscr{R}"),h("\u212D","\\mathfrak{C}"),h("\u210C","\\mathfrak{H}"),h("\u2128","\\mathfrak{Z}"),h("\\Bbbk","\\Bbb{k}"),h("\xB7","\\cdotp"),h("\\llap","\\mathllap{\\textrm{#1}}"),h("\\rlap","\\mathrlap{\\textrm{#1}}"),h("\\clap","\\mathclap{\\textrm{#1}}"),h("\\mathstrut","\\vphantom{(}"),h("\\underbar","\\underline{\\text{#1}}"),h("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}'),h("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`\u2260}}"),h("\\ne","\\neq"),h("\u2260","\\neq"),h("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`\u2209}}"),h("\u2209","\\notin"),h("\u2258","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`\u2258}}"),h("\u2259","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`\u2258}}"),h("\u225A","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`\u225A}}"),h("\u225B","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`\u225B}}"),h("\u225D","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`\u225D}}"),h("\u225E","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`\u225E}}"),h("\u225F","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`\u225F}}"),h("\u27C2","\\perp"),h("\u203C","\\mathclose{!\\mkern-0.8mu!}"),h("\u220C","\\notni"),h("\u231C","\\ulcorner"),h("\u231D","\\urcorner"),h("\u231E","\\llcorner"),h("\u231F","\\lrcorner"),h("\xA9","\\copyright"),h("\xAE","\\textregistered"),h("\uFE0F","\\textregistered"),h("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}'),h("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}'),h("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}'),h("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}'),h("\\vdots","{\\varvdots\\rule{0pt}{15pt}}"),h("\u22EE","\\vdots"),h("\\varGamma","\\mathit{\\Gamma}"),h("\\varDelta","\\mathit{\\Delta}"),h("\\varTheta","\\mathit{\\Theta}"),h("\\varLambda","\\mathit{\\Lambda}"),h("\\varXi","\\mathit{\\Xi}"),h("\\varPi","\\mathit{\\Pi}"),h("\\varSigma","\\mathit{\\Sigma}"),h("\\varUpsilon","\\mathit{\\Upsilon}"),h("\\varPhi","\\mathit{\\Phi}"),h("\\varPsi","\\mathit{\\Psi}"),h("\\varOmega","\\mathit{\\Omega}"),h("\\substack","\\begin{subarray}{c}#1\\end{subarray}"),h("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax"),h("\\boxed","\\fbox{$\\displaystyle{#1}$}"),h("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;"),h("\\implies","\\DOTSB\\;\\Longrightarrow\\;"),h("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;"),h("\\dddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ...}}{#1}}"),h("\\ddddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ....}}{#1}}");let Ur={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};h("\\dots",function(t){let e="\\dotso",r=t.expandAfterFuture().text;return r in Ur?e=Ur[r]:(r.slice(0,4)==="\\not"||r in s0.math&&T.contains(["bin","rel"],s0.math[r].group))&&(e="\\dotsb"),e});let St={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};h("\\dotso",function(t){return t.future().text in St?"\\ldots\\,":"\\ldots"}),h("\\dotsc",function(t){let e=t.future().text;return e in St&&e!==","?"\\ldots\\,":"\\ldots"}),h("\\cdots",function(t){return t.future().text in St?"\\@cdots\\,":"\\@cdots"}),h("\\dotsb","\\cdots"),h("\\dotsm","\\cdots"),h("\\dotsi","\\!\\cdots"),h("\\dotsx","\\ldots\\,"),h("\\DOTSI","\\relax"),h("\\DOTSB","\\relax"),h("\\DOTSX","\\relax"),h("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax"),h("\\,","\\tmspace+{3mu}{.1667em}"),h("\\thinspace","\\,"),h("\\>","\\mskip{4mu}"),h("\\:","\\tmspace+{4mu}{.2222em}"),h("\\medspace","\\:"),h("\\;","\\tmspace+{5mu}{.2777em}"),h("\\thickspace","\\;"),h("\\!","\\tmspace-{3mu}{.1667em}"),h("\\negthinspace","\\!"),h("\\negmedspace","\\tmspace-{4mu}{.2222em}"),h("\\negthickspace","\\tmspace-{5mu}{.277em}"),h("\\enspace","\\kern.5em "),h("\\enskip","\\hskip.5em\\relax"),h("\\quad","\\hskip1em\\relax"),h("\\qquad","\\hskip2em\\relax"),h("\\tag","\\@ifstar\\tag@literal\\tag@paren"),h("\\tag@paren","\\tag@literal{({#1})}"),h("\\tag@literal",t=>{if(t.macros.get("\\df@tag"))throw new v("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"}),h("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}"),h("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)"),h("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}"),h("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1"),h("\\newline","\\\\\\relax"),h("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");let Wr=A(I0["Main-Regular"][84][1]-.7*I0["Main-Regular"][65][1]);h("\\LaTeX","\\textrm{\\html@mathml{"+("L\\kern-.36em\\raisebox{"+Wr+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{LaTeX}}"),h("\\KaTeX","\\textrm{\\html@mathml{"+("K\\kern-.17em\\raisebox{"+Wr+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{KaTeX}}"),h("\\hspace","\\@ifstar\\@hspacer\\@hspace"),h("\\@hspace","\\hskip #1\\relax"),h("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax"),h("\\ordinarycolon",":"),h("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}"),h("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}'),h("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}'),h("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}'),h("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}'),h("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}'),h("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}'),h("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}'),h("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}'),h("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}'),h("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}'),h("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}'),h("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}'),h("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}'),h("\u2237","\\dblcolon"),h("\u2239","\\eqcolon"),h("\u2254","\\coloneqq"),h("\u2255","\\eqqcolon"),h("\u2A74","\\Coloneqq"),h("\\ratio","\\vcentcolon"),h("\\coloncolon","\\dblcolon"),h("\\colonequals","\\coloneqq"),h("\\coloncolonequals","\\Coloneqq"),h("\\equalscolon","\\eqqcolon"),h("\\equalscoloncolon","\\Eqqcolon"),h("\\colonminus","\\coloneq"),h("\\coloncolonminus","\\Coloneq"),h("\\minuscolon","\\eqcolon"),h("\\minuscoloncolon","\\Eqcolon"),h("\\coloncolonapprox","\\Colonapprox"),h("\\coloncolonsim","\\Colonsim"),h("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),h("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}"),h("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),h("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}"),h("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`\u220C}}"),h("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}"),h("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}"),h("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}"),h("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}"),h("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}"),h("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}"),h("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}"),h("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}"),h("\\gvertneqq","\\html@mathml{\\@gvertneqq}{\u2269}"),h("\\lvertneqq","\\html@mathml{\\@lvertneqq}{\u2268}"),h("\\ngeqq","\\html@mathml{\\@ngeqq}{\u2271}"),h("\\ngeqslant","\\html@mathml{\\@ngeqslant}{\u2271}"),h("\\nleqq","\\html@mathml{\\@nleqq}{\u2270}"),h("\\nleqslant","\\html@mathml{\\@nleqslant}{\u2270}"),h("\\nshortmid","\\html@mathml{\\@nshortmid}{\u2224}"),h("\\nshortparallel","\\html@mathml{\\@nshortparallel}{\u2226}"),h("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{\u2288}"),h("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{\u2289}"),h("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{\u228A}"),h("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{\u2ACB}"),h("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{\u228B}"),h("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{\u2ACC}"),h("\\imath","\\html@mathml{\\@imath}{\u0131}"),h("\\jmath","\\html@mathml{\\@jmath}{\u0237}"),h("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`\u27E6}}"),h("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`\u27E7}}"),h("\u27E6","\\llbracket"),h("\u27E7","\\rrbracket"),h("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`\u2983}}"),h("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`\u2984}}"),h("\u2983","\\lBrace"),h("\u2984","\\rBrace"),h("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`\u29B5}}"),h("\u29B5","\\minuso"),h("\\darr","\\downarrow"),h("\\dArr","\\Downarrow"),h("\\Darr","\\Downarrow"),h("\\lang","\\langle"),h("\\rang","\\rangle"),h("\\uarr","\\uparrow"),h("\\uArr","\\Uparrow"),h("\\Uarr","\\Uparrow"),h("\\N","\\mathbb{N}"),h("\\R","\\mathbb{R}"),h("\\Z","\\mathbb{Z}"),h("\\alef","\\aleph"),h("\\alefsym","\\aleph"),h("\\Alpha","\\mathrm{A}"),h("\\Beta","\\mathrm{B}"),h("\\bull","\\bullet"),h("\\Chi","\\mathrm{X}"),h("\\clubs","\\clubsuit"),h("\\cnums","\\mathbb{C}"),h("\\Complex","\\mathbb{C}"),h("\\Dagger","\\ddagger"),h("\\diamonds","\\diamondsuit"),h("\\empty","\\emptyset"),h("\\Epsilon","\\mathrm{E}"),h("\\Eta","\\mathrm{H}"),h("\\exist","\\exists"),h("\\harr","\\leftrightarrow"),h("\\hArr","\\Leftrightarrow"),h("\\Harr","\\Leftrightarrow"),h("\\hearts","\\heartsuit"),h("\\image","\\Im"),h("\\infin","\\infty"),h("\\Iota","\\mathrm{I}"),h("\\isin","\\in"),h("\\Kappa","\\mathrm{K}"),h("\\larr","\\leftarrow"),h("\\lArr","\\Leftarrow"),h("\\Larr","\\Leftarrow"),h("\\lrarr","\\leftrightarrow"),h("\\lrArr","\\Leftrightarrow"),h("\\Lrarr","\\Leftrightarrow"),h("\\Mu","\\mathrm{M}"),h("\\natnums","\\mathbb{N}"),h("\\Nu","\\mathrm{N}"),h("\\Omicron","\\mathrm{O}"),h("\\plusmn","\\pm"),h("\\rarr","\\rightarrow"),h("\\rArr","\\Rightarrow"),h("\\Rarr","\\Rightarrow"),h("\\real","\\Re"),h("\\reals","\\mathbb{R}"),h("\\Reals","\\mathbb{R}"),h("\\Rho","\\mathrm{P}"),h("\\sdot","\\cdot"),h("\\sect","\\S"),h("\\spades","\\spadesuit"),h("\\sub","\\subset"),h("\\sube","\\subseteq"),h("\\supe","\\supseteq"),h("\\Tau","\\mathrm{T}"),h("\\thetasym","\\vartheta"),h("\\weierp","\\wp"),h("\\Zeta","\\mathrm{Z}"),h("\\argmin","\\DOTSB\\operatorname*{arg\\,min}"),h("\\argmax","\\DOTSB\\operatorname*{arg\\,max}"),h("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits"),h("\\bra","\\mathinner{\\langle{#1}|}"),h("\\ket","\\mathinner{|{#1}\\rangle}"),h("\\braket","\\mathinner{\\langle{#1}\\rangle}"),h("\\Bra","\\left\\langle#1\\right|"),h("\\Ket","\\left|#1\\right\\rangle");let Yr=t=>e=>{let r=e.consumeArg().tokens,n=e.consumeArg().tokens,s=e.consumeArg().tokens,a=e.consumeArg().tokens,o=e.macros.get("|"),u=e.macros.get("\\|");e.macros.beginGroup();let m=y=>w=>{t&&(w.macros.set("|",o),s.length&&w.macros.set("\\|",u));let S=y;return!y&&s.length&&w.future().text==="|"&&(w.popToken(),S=!0),{tokens:S?s:n,numArgs:0}};e.macros.set("|",m(!1)),s.length&&e.macros.set("\\|",m(!0));let f=e.consumeArg().tokens,b=e.expandTokens([...a,...f,...r]);return e.macros.endGroup(),{tokens:b.reverse(),numArgs:0}};h("\\bra@ket",Yr(!1)),h("\\bra@set",Yr(!0)),h("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}"),h("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}"),h("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}"),h("\\angln","{\\angl n}"),h("\\blue","\\textcolor{##6495ed}{#1}"),h("\\orange","\\textcolor{##ffa500}{#1}"),h("\\pink","\\textcolor{##ff00af}{#1}"),h("\\red","\\textcolor{##df0030}{#1}"),h("\\green","\\textcolor{##28ae7b}{#1}"),h("\\gray","\\textcolor{gray}{#1}"),h("\\purple","\\textcolor{##9d38bd}{#1}"),h("\\blueA","\\textcolor{##ccfaff}{#1}"),h("\\blueB","\\textcolor{##80f6ff}{#1}"),h("\\blueC","\\textcolor{##63d9ea}{#1}"),h("\\blueD","\\textcolor{##11accd}{#1}"),h("\\blueE","\\textcolor{##0c7f99}{#1}"),h("\\tealA","\\textcolor{##94fff5}{#1}"),h("\\tealB","\\textcolor{##26edd5}{#1}"),h("\\tealC","\\textcolor{##01d1c1}{#1}"),h("\\tealD","\\textcolor{##01a995}{#1}"),h("\\tealE","\\textcolor{##208170}{#1}"),h("\\greenA","\\textcolor{##b6ffb0}{#1}"),h("\\greenB","\\textcolor{##8af281}{#1}"),h("\\greenC","\\textcolor{##74cf70}{#1}"),h("\\greenD","\\textcolor{##1fab54}{#1}"),h("\\greenE","\\textcolor{##0d923f}{#1}"),h("\\goldA","\\textcolor{##ffd0a9}{#1}"),h("\\goldB","\\textcolor{##ffbb71}{#1}"),h("\\goldC","\\textcolor{##ff9c39}{#1}"),h("\\goldD","\\textcolor{##e07d10}{#1}"),h("\\goldE","\\textcolor{##a75a05}{#1}"),h("\\redA","\\textcolor{##fca9a9}{#1}"),h("\\redB","\\textcolor{##ff8482}{#1}"),h("\\redC","\\textcolor{##f9685d}{#1}"),h("\\redD","\\textcolor{##e84d39}{#1}"),h("\\redE","\\textcolor{##bc2612}{#1}"),h("\\maroonA","\\textcolor{##ffbde0}{#1}"),h("\\maroonB","\\textcolor{##ff92c6}{#1}"),h("\\maroonC","\\textcolor{##ed5fa6}{#1}"),h("\\maroonD","\\textcolor{##ca337c}{#1}"),h("\\maroonE","\\textcolor{##9e034e}{#1}"),h("\\purpleA","\\textcolor{##ddd7ff}{#1}"),h("\\purpleB","\\textcolor{##c6b9fc}{#1}"),h("\\purpleC","\\textcolor{##aa87ff}{#1}"),h("\\purpleD","\\textcolor{##7854ab}{#1}"),h("\\purpleE","\\textcolor{##543b78}{#1}"),h("\\mintA","\\textcolor{##f5f9e8}{#1}"),h("\\mintB","\\textcolor{##edf2df}{#1}"),h("\\mintC","\\textcolor{##e0e5cc}{#1}"),h("\\grayA","\\textcolor{##f6f7f7}{#1}"),h("\\grayB","\\textcolor{##f0f1f2}{#1}"),h("\\grayC","\\textcolor{##e3e5e6}{#1}"),h("\\grayD","\\textcolor{##d6d8da}{#1}"),h("\\grayE","\\textcolor{##babec2}{#1}"),h("\\grayF","\\textcolor{##888d93}{#1}"),h("\\grayG","\\textcolor{##626569}{#1}"),h("\\grayH","\\textcolor{##3b3e40}{#1}"),h("\\grayI","\\textcolor{##21242c}{#1}"),h("\\kaBlue","\\textcolor{##314453}{#1}"),h("\\kaGreen","\\textcolor{##71B307}{#1}");let Xr={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class P1{constructor(e,r,n){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=r,this.expansionCount=0,this.feed(e),this.macros=new F1(_1,r.macros),this.mode=n,this.stack=[]}feed(e){this.lexer=new Gr(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return this.stack.length===0&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){let r,n,s;if(e){if(this.consumeSpaces(),this.future().text!=="[")return null;r=this.popToken(),{tokens:s,end:n}=this.consumeArg(["]"])}else({tokens:s,start:r,end:n}=this.consumeArg());return this.pushToken(new B0("EOF",n.loc)),this.pushTokens(s),r.range(n,"")}consumeSpaces(){for(;this.future().text===" ";)this.stack.pop()}consumeArg(e){let r=[],n=e&&e.length>0;n||this.consumeSpaces();let s=this.future(),a,o=0,u=0;do{if(a=this.popToken(),r.push(a),a.text==="{")++o;else if(a.text==="}"){if(--o,o===-1)throw new v("Extra }",a)}else if(a.text==="EOF")throw new v("Unexpected end of input in a macro argument, expected '"+(e&&n?e[u]:"}")+"'",a);if(e&&n)if((o===0||o===1&&e[u]==="{")&&a.text===e[u]){if(++u,u===e.length){r.splice(-u,u);break}}else u=0}while(o!==0||n);return s.text==="{"&&r[r.length-1].text==="}"&&(r.pop(),r.shift()),r.reverse(),{tokens:r,start:s,end:a}}consumeArgs(e,r){if(r){if(r.length!==e+1)throw new v("The length of delimiters doesn't match the number of args!");let s=r[0];for(let a=0;a<s.length;a++){let o=this.popToken();if(s[a]!==o.text)throw new v("Use of the macro doesn't match its definition",o)}}let n=[];for(let s=0;s<e;s++)n.push(this.consumeArg(r&&r[s+1]).tokens);return n}countExpansion(e){if(this.expansionCount+=e,this.expansionCount>this.settings.maxExpand)throw new v("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(e){let r=this.popToken(),n=r.text,s=r.noexpand?null:this._getExpansion(n);if(s==null||e&&s.unexpandable){if(e&&s==null&&n[0]==="\\"&&!this.isDefined(n))throw new v("Undefined control sequence: "+n);return this.pushToken(r),!1}this.countExpansion(1);let a=s.tokens,o=this.consumeArgs(s.numArgs,s.delimiters);if(s.numArgs){a=a.slice();for(let u=a.length-1;u>=0;--u){let m=a[u];if(m.text==="#"){if(u===0)throw new v("Incomplete placeholder at end of macro body",m);if(m=a[--u],m.text==="#")a.splice(u+1,1);else if(/^[1-9]$/.test(m.text))a.splice(u,2,...o[+m.text-1]);else throw new v("Not a valid argument number",m)}}}return this.pushTokens(a),a.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(this.expandOnce()===!1){let e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw new Error}expandMacro(e){return this.macros.has(e)?this.expandTokens([new B0(e)]):void 0}expandTokens(e){let r=[],n=this.stack.length;for(this.pushTokens(e);this.stack.length>n;)if(this.expandOnce(!0)===!1){let s=this.stack.pop();s.treatAsRelax&&(s.noexpand=!1,s.treatAsRelax=!1),r.push(s)}return this.countExpansion(r.length),r}expandMacroAsText(e){let r=this.expandMacro(e);return r&&r.map(n=>n.text).join("")}_getExpansion(e){let r=this.macros.get(e);if(r==null)return r;if(e.length===1){let s=this.lexer.catcodes[e];if(s!=null&&s!==13)return}let n=typeof r=="function"?r(this):r;if(typeof n=="string"){let s=0;if(n.indexOf("#")!==-1){let f=n.replace(/##/g,"");for(;f.indexOf("#"+(s+1))!==-1;)++s}let a=new Gr(n,this.settings),o=[],u=a.lex();for(;u.text!=="EOF";)o.push(u),u=a.lex();return o.reverse(),{tokens:o,numArgs:s}}return n}isDefined(e){return this.macros.has(e)||K0.hasOwnProperty(e)||s0.math.hasOwnProperty(e)||s0.text.hasOwnProperty(e)||Xr.hasOwnProperty(e)}isExpandable(e){let r=this.macros.get(e);return r!=null?typeof r=="string"||typeof r=="function"||!r.unexpandable:K0.hasOwnProperty(e)&&!K0[e].primitive}}let jr=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,He=Object.freeze({"\u208A":"+","\u208B":"-","\u208C":"=","\u208D":"(","\u208E":")","\u2080":"0","\u2081":"1","\u2082":"2","\u2083":"3","\u2084":"4","\u2085":"5","\u2086":"6","\u2087":"7","\u2088":"8","\u2089":"9","\u2090":"a","\u2091":"e","\u2095":"h","\u1D62":"i","\u2C7C":"j","\u2096":"k","\u2097":"l","\u2098":"m","\u2099":"n","\u2092":"o","\u209A":"p","\u1D63":"r","\u209B":"s","\u209C":"t","\u1D64":"u","\u1D65":"v","\u2093":"x","\u1D66":"\u03B2","\u1D67":"\u03B3","\u1D68":"\u03C1","\u1D69":"\u03D5","\u1D6A":"\u03C7","\u207A":"+","\u207B":"-","\u207C":"=","\u207D":"(","\u207E":")","\u2070":"0","\xB9":"1","\xB2":"2","\xB3":"3","\u2074":"4","\u2075":"5","\u2076":"6","\u2077":"7","\u2078":"8","\u2079":"9","\u1D2C":"A","\u1D2E":"B","\u1D30":"D","\u1D31":"E","\u1D33":"G","\u1D34":"H","\u1D35":"I","\u1D36":"J","\u1D37":"K","\u1D38":"L","\u1D39":"M","\u1D3A":"N","\u1D3C":"O","\u1D3E":"P","\u1D3F":"R","\u1D40":"T","\u1D41":"U","\u2C7D":"V","\u1D42":"W","\u1D43":"a","\u1D47":"b","\u1D9C":"c","\u1D48":"d","\u1D49":"e","\u1DA0":"f","\u1D4D":"g",\u02B0:"h","\u2071":"i",\u02B2:"j","\u1D4F":"k",\u02E1:"l","\u1D50":"m",\u207F:"n","\u1D52":"o","\u1D56":"p",\u02B3:"r",\u02E2:"s","\u1D57":"t","\u1D58":"u","\u1D5B":"v",\u02B7:"w",\u02E3:"x",\u02B8:"y","\u1DBB":"z","\u1D5D":"\u03B2","\u1D5E":"\u03B3","\u1D5F":"\u03B4","\u1D60":"\u03D5","\u1D61":"\u03C7","\u1DBF":"\u03B8"}),Mt={"\u0301":{text:"\\'",math:"\\acute"},"\u0300":{text:"\\`",math:"\\grave"},"\u0308":{text:'\\"',math:"\\ddot"},"\u0303":{text:"\\~",math:"\\tilde"},"\u0304":{text:"\\=",math:"\\bar"},"\u0306":{text:"\\u",math:"\\breve"},"\u030C":{text:"\\v",math:"\\check"},"\u0302":{text:"\\^",math:"\\hat"},"\u0307":{text:"\\.",math:"\\dot"},"\u030A":{text:"\\r",math:"\\mathring"},"\u030B":{text:"\\H"},"\u0327":{text:"\\c"}},Zr={\u00E1:"a\u0301",\u00E0:"a\u0300",\u00E4:"a\u0308",\u01DF:"a\u0308\u0304",\u00E3:"a\u0303",\u0101:"a\u0304",\u0103:"a\u0306",\u1EAF:"a\u0306\u0301",\u1EB1:"a\u0306\u0300",\u1EB5:"a\u0306\u0303",\u01CE:"a\u030C",\u00E2:"a\u0302",\u1EA5:"a\u0302\u0301",\u1EA7:"a\u0302\u0300",\u1EAB:"a\u0302\u0303",\u0227:"a\u0307",\u01E1:"a\u0307\u0304",\u00E5:"a\u030A",\u01FB:"a\u030A\u0301",\u1E03:"b\u0307",\u0107:"c\u0301",\u1E09:"c\u0327\u0301",\u010D:"c\u030C",\u0109:"c\u0302",\u010B:"c\u0307",\u00E7:"c\u0327",\u010F:"d\u030C",\u1E0B:"d\u0307",\u1E11:"d\u0327",\u00E9:"e\u0301",\u00E8:"e\u0300",\u00EB:"e\u0308",\u1EBD:"e\u0303",\u0113:"e\u0304",\u1E17:"e\u0304\u0301",\u1E15:"e\u0304\u0300",\u0115:"e\u0306",\u1E1D:"e\u0327\u0306",\u011B:"e\u030C",\u00EA:"e\u0302",\u1EBF:"e\u0302\u0301",\u1EC1:"e\u0302\u0300",\u1EC5:"e\u0302\u0303",\u0117:"e\u0307",\u0229:"e\u0327",\u1E1F:"f\u0307",\u01F5:"g\u0301",\u1E21:"g\u0304",\u011F:"g\u0306",\u01E7:"g\u030C",\u011D:"g\u0302",\u0121:"g\u0307",\u0123:"g\u0327",\u1E27:"h\u0308",\u021F:"h\u030C",\u0125:"h\u0302",\u1E23:"h\u0307",\u1E29:"h\u0327",\u00ED:"i\u0301",\u00EC:"i\u0300",\u00EF:"i\u0308",\u1E2F:"i\u0308\u0301",\u0129:"i\u0303",\u012B:"i\u0304",\u012D:"i\u0306",\u01D0:"i\u030C",\u00EE:"i\u0302",\u01F0:"j\u030C",\u0135:"j\u0302",\u1E31:"k\u0301",\u01E9:"k\u030C",\u0137:"k\u0327",\u013A:"l\u0301",\u013E:"l\u030C",\u013C:"l\u0327",\u1E3F:"m\u0301",\u1E41:"m\u0307",\u0144:"n\u0301",\u01F9:"n\u0300",\u00F1:"n\u0303",\u0148:"n\u030C",\u1E45:"n\u0307",\u0146:"n\u0327",\u00F3:"o\u0301",\u00F2:"o\u0300",\u00F6:"o\u0308",\u022B:"o\u0308\u0304",\u00F5:"o\u0303",\u1E4D:"o\u0303\u0301",\u1E4F:"o\u0303\u0308",\u022D:"o\u0303\u0304",\u014D:"o\u0304",\u1E53:"o\u0304\u0301",\u1E51:"o\u0304\u0300",\u014F:"o\u0306",\u01D2:"o\u030C",\u00F4:"o\u0302",\u1ED1:"o\u0302\u0301",\u1ED3:"o\u0302\u0300",\u1ED7:"o\u0302\u0303",\u022F:"o\u0307",\u0231:"o\u0307\u0304",\u0151:"o\u030B",\u1E55:"p\u0301",\u1E57:"p\u0307",\u0155:"r\u0301",\u0159:"r\u030C",\u1E59:"r\u0307",\u0157:"r\u0327",\u015B:"s\u0301",\u1E65:"s\u0301\u0307",\u0161:"s\u030C",\u1E67:"s\u030C\u0307",\u015D:"s\u0302",\u1E61:"s\u0307",\u015F:"s\u0327",\u1E97:"t\u0308",\u0165:"t\u030C",\u1E6B:"t\u0307",\u0163:"t\u0327",\u00FA:"u\u0301",\u00F9:"u\u0300",\u00FC:"u\u0308",\u01D8:"u\u0308\u0301",\u01DC:"u\u0308\u0300",\u01D6:"u\u0308\u0304",\u01DA:"u\u0308\u030C",\u0169:"u\u0303",\u1E79:"u\u0303\u0301",\u016B:"u\u0304",\u1E7B:"u\u0304\u0308",\u016D:"u\u0306",\u01D4:"u\u030C",\u00FB:"u\u0302",\u016F:"u\u030A",\u0171:"u\u030B",\u1E7D:"v\u0303",\u1E83:"w\u0301",\u1E81:"w\u0300",\u1E85:"w\u0308",\u0175:"w\u0302",\u1E87:"w\u0307",\u1E98:"w\u030A",\u1E8D:"x\u0308",\u1E8B:"x\u0307",\u00FD:"y\u0301",\u1EF3:"y\u0300",\u00FF:"y\u0308",\u1EF9:"y\u0303",\u0233:"y\u0304",\u0177:"y\u0302",\u1E8F:"y\u0307",\u1E99:"y\u030A",\u017A:"z\u0301",\u017E:"z\u030C",\u1E91:"z\u0302",\u017C:"z\u0307",\u00C1:"A\u0301",\u00C0:"A\u0300",\u00C4:"A\u0308",\u01DE:"A\u0308\u0304",\u00C3:"A\u0303",\u0100:"A\u0304",\u0102:"A\u0306",\u1EAE:"A\u0306\u0301",\u1EB0:"A\u0306\u0300",\u1EB4:"A\u0306\u0303",\u01CD:"A\u030C",\u00C2:"A\u0302",\u1EA4:"A\u0302\u0301",\u1EA6:"A\u0302\u0300",\u1EAA:"A\u0302\u0303",\u0226:"A\u0307",\u01E0:"A\u0307\u0304",\u00C5:"A\u030A",\u01FA:"A\u030A\u0301",\u1E02:"B\u0307",\u0106:"C\u0301",\u1E08:"C\u0327\u0301",\u010C:"C\u030C",\u0108:"C\u0302",\u010A:"C\u0307",\u00C7:"C\u0327",\u010E:"D\u030C",\u1E0A:"D\u0307",\u1E10:"D\u0327",\u00C9:"E\u0301",\u00C8:"E\u0300",\u00CB:"E\u0308",\u1EBC:"E\u0303",\u0112:"E\u0304",\u1E16:"E\u0304\u0301",\u1E14:"E\u0304\u0300",\u0114:"E\u0306",\u1E1C:"E\u0327\u0306",\u011A:"E\u030C",\u00CA:"E\u0302",\u1EBE:"E\u0302\u0301",\u1EC0:"E\u0302\u0300",\u1EC4:"E\u0302\u0303",\u0116:"E\u0307",\u0228:"E\u0327",\u1E1E:"F\u0307",\u01F4:"G\u0301",\u1E20:"G\u0304",\u011E:"G\u0306",\u01E6:"G\u030C",\u011C:"G\u0302",\u0120:"G\u0307",\u0122:"G\u0327",\u1E26:"H\u0308",\u021E:"H\u030C",\u0124:"H\u0302",\u1E22:"H\u0307",\u1E28:"H\u0327",\u00CD:"I\u0301",\u00CC:"I\u0300",\u00CF:"I\u0308",\u1E2E:"I\u0308\u0301",\u0128:"I\u0303",\u012A:"I\u0304",\u012C:"I\u0306",\u01CF:"I\u030C",\u00CE:"I\u0302",\u0130:"I\u0307",\u0134:"J\u0302",\u1E30:"K\u0301",\u01E8:"K\u030C",\u0136:"K\u0327",\u0139:"L\u0301",\u013D:"L\u030C",\u013B:"L\u0327",\u1E3E:"M\u0301",\u1E40:"M\u0307",\u0143:"N\u0301",\u01F8:"N\u0300",\u00D1:"N\u0303",\u0147:"N\u030C",\u1E44:"N\u0307",\u0145:"N\u0327",\u00D3:"O\u0301",\u00D2:"O\u0300",\u00D6:"O\u0308",\u022A:"O\u0308\u0304",\u00D5:"O\u0303",\u1E4C:"O\u0303\u0301",\u1E4E:"O\u0303\u0308",\u022C:"O\u0303\u0304",\u014C:"O\u0304",\u1E52:"O\u0304\u0301",\u1E50:"O\u0304\u0300",\u014E:"O\u0306",\u01D1:"O\u030C",\u00D4:"O\u0302",\u1ED0:"O\u0302\u0301",\u1ED2:"O\u0302\u0300",\u1ED6:"O\u0302\u0303",\u022E:"O\u0307",\u0230:"O\u0307\u0304",\u0150:"O\u030B",\u1E54:"P\u0301",\u1E56:"P\u0307",\u0154:"R\u0301",\u0158:"R\u030C",\u1E58:"R\u0307",\u0156:"R\u0327",\u015A:"S\u0301",\u1E64:"S\u0301\u0307",\u0160:"S\u030C",\u1E66:"S\u030C\u0307",\u015C:"S\u0302",\u1E60:"S\u0307",\u015E:"S\u0327",\u0164:"T\u030C",\u1E6A:"T\u0307",\u0162:"T\u0327",\u00DA:"U\u0301",\u00D9:"U\u0300",\u00DC:"U\u0308",\u01D7:"U\u0308\u0301",\u01DB:"U\u0308\u0300",\u01D5:"U\u0308\u0304",\u01D9:"U\u0308\u030C",\u0168:"U\u0303",\u1E78:"U\u0303\u0301",\u016A:"U\u0304",\u1E7A:"U\u0304\u0308",\u016C:"U\u0306",\u01D3:"U\u030C",\u00DB:"U\u0302",\u016E:"U\u030A",\u0170:"U\u030B",\u1E7C:"V\u0303",\u1E82:"W\u0301",\u1E80:"W\u0300",\u1E84:"W\u0308",\u0174:"W\u0302",\u1E86:"W\u0307",\u1E8C:"X\u0308",\u1E8A:"X\u0307",\u00DD:"Y\u0301",\u1EF2:"Y\u0300",\u0178:"Y\u0308",\u1EF8:"Y\u0303",\u0232:"Y\u0304",\u0176:"Y\u0302",\u1E8E:"Y\u0307",\u0179:"Z\u0301",\u017D:"Z\u030C",\u1E90:"Z\u0302",\u017B:"Z\u0307",\u03AC:"\u03B1\u0301",\u1F70:"\u03B1\u0300",\u1FB1:"\u03B1\u0304",\u1FB0:"\u03B1\u0306",\u03AD:"\u03B5\u0301",\u1F72:"\u03B5\u0300",\u03AE:"\u03B7\u0301",\u1F74:"\u03B7\u0300",\u03AF:"\u03B9\u0301",\u1F76:"\u03B9\u0300",\u03CA:"\u03B9\u0308",\u0390:"\u03B9\u0308\u0301",\u1FD2:"\u03B9\u0308\u0300",\u1FD1:"\u03B9\u0304",\u1FD0:"\u03B9\u0306",\u03CC:"\u03BF\u0301",\u1F78:"\u03BF\u0300",\u03CD:"\u03C5\u0301",\u1F7A:"\u03C5\u0300",\u03CB:"\u03C5\u0308",\u03B0:"\u03C5\u0308\u0301",\u1FE2:"\u03C5\u0308\u0300",\u1FE1:"\u03C5\u0304",\u1FE0:"\u03C5\u0306",\u03CE:"\u03C9\u0301",\u1F7C:"\u03C9\u0300",\u038E:"\u03A5\u0301",\u1FEA:"\u03A5\u0300",\u03AB:"\u03A5\u0308",\u1FE9:"\u03A5\u0304",\u1FE8:"\u03A5\u0306",\u038F:"\u03A9\u0301",\u1FFA:"\u03A9\u0300"};class Le{constructor(e,r){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new P1(e,r,this.mode),this.settings=r,this.leftrightDepth=0}expect(e,r){if(r===void 0&&(r=!0),this.fetch().text!==e)throw new v("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());r&&this.consume()}consume(){this.nextToken=null}fetch(){return this.nextToken==null&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{let e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){let r=this.nextToken;this.consume(),this.gullet.pushToken(new B0("}")),this.gullet.pushTokens(e);let n=this.parseExpression(!1);return this.expect("}"),this.nextToken=r,n}parseExpression(e,r){let n=[];for(;;){this.mode==="math"&&this.consumeSpaces();let s=this.fetch();if(Le.endOfExpression.indexOf(s.text)!==-1||r&&s.text===r||e&&K0[s.text]&&K0[s.text].infix)break;let a=this.parseAtom(r);if(a){if(a.type==="internal")continue}else break;n.push(a)}return this.mode==="text"&&this.formLigatures(n),this.handleInfixNodes(n)}handleInfixNodes(e){let r=-1,n;for(let s=0;s<e.length;s++)if(e[s].type==="infix"){if(r!==-1)throw new v("only one infix operator per group",e[s].token);r=s,n=e[s].replaceWith}if(r!==-1&&n){let s,a,o=e.slice(0,r),u=e.slice(r+1);o.length===1&&o[0].type==="ordgroup"?s=o[0]:s={type:"ordgroup",mode:this.mode,body:o},u.length===1&&u[0].type==="ordgroup"?a=u[0]:a={type:"ordgroup",mode:this.mode,body:u};let m;return n==="\\\\abovefrac"?m=this.callFunction(n,[s,e[r],a],[]):m=this.callFunction(n,[s,a],[]),[m]}else return e}handleSupSubscript(e){let r=this.fetch(),n=r.text;this.consume(),this.consumeSpaces();let s=this.parseGroup(e);if(!s)throw new v("Expected group after '"+n+"'",r);return s}formatUnsupportedCmd(e){let r=[];for(let a=0;a<e.length;a++)r.push({type:"textord",mode:"text",text:e[a]});let n={type:"text",mode:this.mode,body:r};return{type:"color",mode:this.mode,color:this.settings.errorColor,body:[n]}}parseAtom(e){let r=this.parseGroup("atom",e);if(this.mode==="text")return r;let n,s;for(;;){this.consumeSpaces();let a=this.fetch();if(a.text==="\\limits"||a.text==="\\nolimits"){if(r&&r.type==="op"){let o=a.text==="\\limits";r.limits=o,r.alwaysHandleSupSub=!0}else if(r&&r.type==="operatorname")r.alwaysHandleSupSub&&(r.limits=a.text==="\\limits");else throw new v("Limit controls must follow a math operator",a);this.consume()}else if(a.text==="^"){if(n)throw new v("Double superscript",a);n=this.handleSupSubscript("superscript")}else if(a.text==="_"){if(s)throw new v("Double subscript",a);s=this.handleSupSubscript("subscript")}else if(a.text==="'"){if(n)throw new v("Double superscript",a);let o={type:"textord",mode:this.mode,text:"\\prime"},u=[o];for(this.consume();this.fetch().text==="'";)u.push(o),this.consume();this.fetch().text==="^"&&u.push(this.handleSupSubscript("superscript")),n={type:"ordgroup",mode:this.mode,body:u}}else if(He[a.text]){let o=jr.test(a.text),u=[];for(u.push(new B0(He[a.text])),this.consume();;){let f=this.fetch().text;if(!He[f]||jr.test(f)!==o)break;u.unshift(new B0(He[f])),this.consume()}let m=this.subparse(u);o?s={type:"ordgroup",mode:"math",body:m}:n={type:"ordgroup",mode:"math",body:m}}else break}return n||s?{type:"supsub",mode:this.mode,base:r,sup:n,sub:s}:r}parseFunction(e,r){let n=this.fetch(),s=n.text,a=K0[s];if(!a)return null;if(this.consume(),r&&r!=="atom"&&!a.allowedInArgument)throw new v("Got function '"+s+"' with no arguments"+(r?" as "+r:""),n);if(this.mode==="text"&&!a.allowedInText)throw new v("Can't use function '"+s+"' in text mode",n);if(this.mode==="math"&&a.allowedInMath===!1)throw new v("Can't use function '"+s+"' in math mode",n);let{args:o,optArgs:u}=this.parseArguments(s,a);return this.callFunction(s,o,u,n,e)}callFunction(e,r,n,s,a){let o={funcName:e,parser:this,token:s,breakOnTokenText:a},u=K0[e];if(u&&u.handler)return u.handler(o,r,n);throw new v("No function handler for "+e)}parseArguments(e,r){let n=r.numArgs+r.numOptionalArgs;if(n===0)return{args:[],optArgs:[]};let s=[],a=[];for(let o=0;o<n;o++){let u=r.argTypes&&r.argTypes[o],m=o<r.numOptionalArgs;(r.primitive&&u==null||r.type==="sqrt"&&o===1&&a[0]==null)&&(u="primitive");let f=this.parseGroupOfType("argument to '"+e+"'",u,m);if(m)a.push(f);else if(f!=null)s.push(f);else throw new v("Null argument, please report this as a bug")}return{args:s,optArgs:a}}parseGroupOfType(e,r,n){switch(r){case"color":return this.parseColorGroup(n);case"size":return this.parseSizeGroup(n);case"url":return this.parseUrlGroup(n);case"math":case"text":return this.parseArgumentGroup(n,r);case"hbox":{let s=this.parseArgumentGroup(n,"text");return s!=null?{type:"styling",mode:s.mode,body:[s],style:"text"}:null}case"raw":{let s=this.parseStringGroup("raw",n);return s!=null?{type:"raw",mode:"text",string:s.text}:null}case"primitive":{if(n)throw new v("A primitive argument cannot be optional");let s=this.parseGroup(e);if(s==null)throw new v("Expected group as "+e,this.fetch());return s}case"original":case null:case void 0:return this.parseArgumentGroup(n);default:throw new v("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;this.fetch().text===" ";)this.consume()}parseStringGroup(e,r){let n=this.gullet.scanArgument(r);if(n==null)return null;let s="",a;for(;(a=this.fetch()).text!=="EOF";)s+=a.text,this.consume();return this.consume(),n.text=s,n}parseRegexGroup(e,r){let n=this.fetch(),s=n,a="",o;for(;(o=this.fetch()).text!=="EOF"&&e.test(a+o.text);)s=o,a+=s.text,this.consume();if(a==="")throw new v("Invalid "+r+": '"+n.text+"'",n);return n.range(s,a)}parseColorGroup(e){let r=this.parseStringGroup("color",e);if(r==null)return null;let n=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(r.text);if(!n)throw new v("Invalid color: '"+r.text+"'",r);let s=n[0];return/^[0-9a-f]{6}$/i.test(s)&&(s="#"+s),{type:"color-token",mode:this.mode,color:s}}parseSizeGroup(e){let r,n=!1;if(this.gullet.consumeSpaces(),!e&&this.gullet.future().text!=="{"?r=this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size"):r=this.parseStringGroup("size",e),!r)return null;!e&&r.text.length===0&&(r.text="0pt",n=!0);let s=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(r.text);if(!s)throw new v("Invalid size: '"+r.text+"'",r);let a={number:+(s[1]+s[2]),unit:s[3]};if(!Et(a))throw new v("Invalid unit: '"+a.unit+"'",r);return{type:"size",mode:this.mode,value:a,isBlank:n}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);let r=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),r==null)return null;let n=r.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:n}}parseArgumentGroup(e,r){let n=this.gullet.scanArgument(e);if(n==null)return null;let s=this.mode;r&&this.switchMode(r),this.gullet.beginGroup();let a=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();let o={type:"ordgroup",mode:this.mode,loc:n.loc,body:a};return r&&this.switchMode(s),o}parseGroup(e,r){let n=this.fetch(),s=n.text,a;if(s==="{"||s==="\\begingroup"){this.consume();let o=s==="{"?"}":"\\endgroup";this.gullet.beginGroup();let u=this.parseExpression(!1,o),m=this.fetch();this.expect(o),this.gullet.endGroup(),a={type:"ordgroup",mode:this.mode,loc:M0.range(n,m),body:u,semisimple:s==="\\begingroup"||void 0}}else if(a=this.parseFunction(r,e)||this.parseSymbol(),a==null&&s[0]==="\\"&&!Xr.hasOwnProperty(s)){if(this.settings.throwOnError)throw new v("Undefined control sequence: "+s,n);a=this.formatUnsupportedCmd(s),this.consume()}return a}formLigatures(e){let r=e.length-1;for(let n=0;n<r;++n){let s=e[n],a=s.text;a==="-"&&e[n+1].text==="-"&&(n+1<r&&e[n+2].text==="-"?(e.splice(n,3,{type:"textord",mode:"text",loc:M0.range(s,e[n+2]),text:"---"}),r-=2):(e.splice(n,2,{type:"textord",mode:"text",loc:M0.range(s,e[n+1]),text:"--"}),r-=1)),(a==="'"||a==="`")&&e[n+1].text===a&&(e.splice(n,2,{type:"textord",mode:"text",loc:M0.range(s,e[n+1]),text:a+a}),r-=1)}}parseSymbol(){let e=this.fetch(),r=e.text;if(/^\\verb[^a-zA-Z]/.test(r)){this.consume();let a=r.slice(5),o=a.charAt(0)==="*";if(o&&(a=a.slice(1)),a.length<2||a.charAt(0)!==a.slice(-1))throw new v(`\\verb assertion failed --
                    please report what input caused this bug`);return a=a.slice(1,-1),{type:"verb",mode:"text",body:a,star:o}}Zr.hasOwnProperty(r[0])&&!s0[this.mode][r[0]]&&(this.settings.strict&&this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+r[0]+'" used in math mode',e),r=Zr[r[0]]+r.slice(1));let n=H1.exec(r);n&&(r=r.substring(0,n.index),r==="i"?r="\u0131":r==="j"&&(r="\u0237"));let s;if(s0[this.mode][r]){this.settings.strict&&this.mode==="math"&&Ye.indexOf(r)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+r[0]+'" used in math mode',e);let a=s0[this.mode][r].group,o=M0.range(e),u;if(Hn.hasOwnProperty(a)){let m=a;u={type:"atom",mode:this.mode,family:m,loc:o,text:r}}else u={type:a,mode:this.mode,loc:o,text:r};s=u}else if(r.charCodeAt(0)>=128)this.settings.strict&&(Bt(r.charCodeAt(0))?this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+r[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+r[0]+'"'+(" ("+r.charCodeAt(0)+")"),e)),s={type:"textord",mode:"text",loc:M0.range(e),text:r};else return null;if(this.consume(),n)for(let a=0;a<n[0].length;a++){let o=n[0][a];if(!Mt[o])throw new v("Unknown accent ' "+o+"'",e);let u=Mt[o][this.mode]||Mt[o].text;if(!u)throw new v("Accent "+o+" unsupported in "+this.mode+" mode",e);s={type:"accent",mode:this.mode,loc:M0.range(e),label:u,isStretchy:!1,isShifty:!0,base:s}}return s}}Le.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var zt=function(t,e){if(!(typeof t=="string"||t instanceof String))throw new TypeError("KaTeX can only parse string typed expression");let r=new Le(t,e);delete r.gullet.macros.current["\\df@tag"];let n=r.parse();if(delete r.gullet.macros.current["\\current@color"],delete r.gullet.macros.current["\\color"],r.gullet.macros.get("\\df@tag")){if(!e.displayMode)throw new v("\\tag works only in display equations");n=[{type:"tag",mode:"text",body:n,tag:r.subparse([new B0("\\df@tag")])}]}return n};let Kr=function(t,e,r){e.textContent="";let n=At(t,r).toNode();e.appendChild(n)};typeof document<"u"&&document.compatMode!=="CSS1Compat"&&(typeof console<"u"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),Kr=function(){throw new v("KaTeX doesn't work in quirks mode.")});let V1=function(t,e){return At(t,e).toMarkup()},G1=function(t,e){let r=new oe(e);return zt(t,r)},Jr=function(t,e,r){if(r.throwOnError||!(t instanceof v))throw t;let n=x.makeSpan(["katex-error"],[new A0(e)]);return n.setAttribute("title",t.toString()),n.setAttribute("style","color:"+r.errorColor),n},At=function(t,e){let r=new oe(e);try{let n=zt(t,r);return s1(n,t,r)}catch(n){return Jr(n,t,r)}};var $1={version:"0.16.21",render:Kr,renderToString:V1,ParseError:v,SETTINGS_SCHEMA:f0,__parse:G1,__renderToDomTree:At,__renderToHTMLTree:function(t,e){let r=new oe(e);try{let n=zt(t,r);return i1(n,t,r)}catch(n){return Jr(n,t,r)}},__setFontMetrics:Bn,__defineSymbol:i,__defineFunction:B,__defineMacro:h,__domTree:{Span:he,Anchor:Ue,SymbolNode:A0,SvgNode:P0,PathNode:X0,LineNode:We}},U1=$1;return N=N.default,N}()})});var cn=tn(be=>{"use strict";var X1=be&&be.__importDefault||function(k){return k&&k.__esModule?k:{default:k}};Object.defineProperty(be,"__esModule",{value:!0});var j1=X1(rn());function nn(k,N){let H=k.src[N-1],v=k.src[N],L=k.src[N+1];if(v!=="$")return{can_open:!1,can_close:!1};let R=!1,P=!1;return H!=="$"&&H!=="\\"&&(H===void 0||sn(H)||!an(H))&&(R=!0),L!=="$"&&(L==null||sn(L)||!an(L))&&(P=!0),{can_open:R,can_close:P}}function sn(k){return/^\s$/u.test(k)}function an(k){return/^[\w\d]$/u.test(k)}function ln(k,N){let H=k.src[N-1],v=k.src[N],L=k.src[N+1],R=k.src[N+2];return v==="$"&&H!=="$"&&H!=="\\"&&L==="$"&&R!=="$"?{can_open:!0,can_close:!0}:{can_open:!1,can_close:!1}}function Z1(k,N){if(k.src[k.pos]!=="$")return!1;let H=k.tokens.at(-1);if(H?.type==="html_inline"&&/^<\w+.+[^/]>$/.test(H.content))return!1;let v=nn(k,k.pos);if(!v.can_open)return N||(k.pending+="$"),k.pos+=1,!0;let L=k.pos+1,R=L,P;for(;(R=k.src.indexOf("$",R))!==-1;){for(P=R-1;k.src[P]==="\\";)P-=1;if((R-P)%2==1)break;R+=1}if(R===-1)return N||(k.pending+="$"),k.pos=L,!0;if(R-L===0)return N||(k.pending+="$$"),k.pos=L+1,!0;if(v=nn(k,R),!v.can_close)return N||(k.pending+="$"),k.pos=L,!0;if(!N){let r0=k.push("math_inline","math",0);r0.markup="$",r0.content=k.src.slice(L,R)}return k.pos=R+1,!0}function K1(k,N,H,v){var L,R,P,r0=!1,o0,$=k.bMarks[N]+k.tShift[N],Q=k.eMarks[N];if($+2>Q||k.src.slice($,$+2)!=="$$")return!1;$+=2;let c0=k.src.slice($,Q);if(v)return!0;for(c0.trim().slice(-2)==="$$"&&(c0=c0.trim().slice(0,-2),r0=!0),R=N;!r0&&(R++,!(R>=H||($=k.bMarks[R]+k.tShift[R],Q=k.eMarks[R],$<Q&&k.tShift[R]<k.blkIndent)));)k.src.slice($,Q).trim().slice(-2)==="$$"?(P=k.src.slice(0,Q).lastIndexOf("$$"),L=k.src.slice($,P),r0=!0):k.src.slice($,Q).trim().includes("$$")&&(P=k.src.slice(0,Q).trim().indexOf("$$"),L=k.src.slice($,P),r0=!0);return k.line=R+1,o0=k.push("math_block","math",0),o0.block=!0,o0.content=(c0&&c0.trim()?c0+`
`:"")+k.getLines(N+1,R,k.tShift[N],!0)+(L&&L.trim()?L:""),o0.map=[N,k.line],o0.markup="$$",!0}function J1(k,N,H,v){let L=k.bMarks[N]+k.tShift[N],R=k.eMarks[N];if(!k.src.slice(L,R).match(/^\s*\\begin\s*\{([^{}]+)\}/))return!1;if(N>0){let j=k.bMarks[N-1]+k.tShift[N-1],h0=k.eMarks[N-1],T=k.src.slice(j,h0);if(!/^\s*$/.test(T))return!1}if(v)return!0;let o0=[],$=N,Q,c0=!1;e:for(;!c0&&!($>=H);$++){let j=k.bMarks[$]+k.tShift[$],h0=k.eMarks[$];if(j<h0&&k.tShift[$]<k.blkIndent)break;let T=k.src.slice(j,h0);for(let f0 of T.matchAll(/(\\begin|\\end)\s*\{([^{}]+)\}/g))if(f0[1]==="\\begin")o0.push(f0[2].trim());else if(f0[1]==="\\end"&&(o0.pop(),!o0.length)){Q=k.src.slice(j,h0),c0=!0;break e}}k.line=$+1;let k0=k.push("math_block","math",0);return k0.block=!0,k0.content=(k.getLines(N,$,k.tShift[N],!0)+(Q??"")).trim(),k0.map=[N,k.line],k0.markup="$$",!0}function Q1(k,N){var H,v,L,R,P;if(k.src.slice(k.pos,k.pos+2)!=="$$")return!1;if(R=ln(k,k.pos),!R.can_open)return N||(k.pending+="$$"),k.pos+=2,!0;for(H=k.pos+2,v=H;(v=k.src.indexOf("$$",v))!==-1;){for(P=v-1;k.src[P]==="\\";)P-=1;if((v-P)%2==1)break;v+=2}return v===-1?(N||(k.pending+="$$"),k.pos=H,!0):v-H===0?(N||(k.pending+="$$$$"),k.pos=H+2,!0):(R=ln(k,v),R.can_close?(N||(L=k.push("math_block","math",0),L.block=!0,L.markup="$$",L.content=k.src.slice(H,v)),k.pos=v+2,!0):(N||(k.pending+="$$"),k.pos=H,!0))}function es(k,N){let H=k.src.slice(k.pos);if(!/^\n\\begin/.test(H))return!1;if(k.pos+=1,N)return!0;let v=H.split(/\n/g).slice(1),L,R=[];e:for(var P=0;P<v.length;++P){let $=v[P];for(let Q of $.matchAll(/(\\begin|\\end)\s*\{([^{}]+)\}/g))if(Q[1]==="\\begin")R.push(Q[2].trim());else if(Q[1]==="\\end"&&(R.pop(),!R.length)){L=P;break e}}if(typeof L>"u")return!1;let r0=v.slice(0,L+1).reduce(($,Q)=>$+Q.length,0)+L+1,o0=k.push("math_inline_bare_block","math",0);return o0.block=!0,o0.markup="$$",o0.content=H.slice(1,r0),k.pos=k.pos+r0,!0}function on(k,N,H,v){let L=k.tokens;for(let R=L.length-1;R>=0;R--){let P=L[R],r0=[];if(P.type!=="html_block")continue;let o0=P.content;for(let $ of o0.matchAll(v)){if(!$.groups)continue;let Q=$.groups.html_before_math,c0=$.groups.math,k0=$.groups.html_after_math;Q&&r0.push({...P,type:"html_block",map:null,content:Q}),c0&&r0.push({...P,type:N,map:null,content:c0,markup:H,block:!0,tag:"math"}),k0&&r0.push({...P,type:"html_block",map:null,content:k0})}r0.length>0&&L.splice(R,1,...r0)}return!0}function _e(k){return k.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")}function ts(k,N){let H=N?.katex??j1.default,v=N?.enableBareBlocks,L=N?.enableMathBlockInHtml,R=N?.enableMathInlineInHtml,P=N?.enableFencedBlocks;k.inline.ruler.after("escape","math_inline",Z1),k.inline.ruler.after("escape","math_inline_block",Q1),v&&k.inline.ruler.before("text","math_inline_bare_block",es),k.block.ruler.after("blockquote","math_block",(j,h0,T,f0)=>v&&J1(j,h0,T,f0)?!0:K1(j,h0,T,f0),{alt:["paragraph","reference","blockquote","list"]});let r0=/(?<html_before_math>[\s\S]*?)\$\$(?<math>[\s\S]+?)\$\$(?<html_after_math>(?:(?!\$\$[\s\S]+?\$\$)[\s\S])*)/gm,o0=/(?<html_before_math>[\s\S]*?)\$(?<math>.*?)\$(?<html_after_math>(?:(?!\$.*?\$)[\s\S])*)/gm;L&&k.core.ruler.push("math_block_in_html_block",j=>on(j,"math_block","$$",r0)),R&&k.core.ruler.push("math_inline_in_html_block",j=>on(j,"math_inline","$",o0));let $=j=>{let h0=/\\begin\{(align|equation|gather|cd|alignat)\}/ig.test(j);try{return H.renderToString(j,{...N,displayMode:h0})}catch(T){return N?.throwOnError&&console.log(T),`<span class="katex-error" title="${_e(j)}">${_e(T+"")}</span>`}},Q=(j,h0)=>{let T=j[h0].content,le=T.length>2&&T[0]==="`"&&T[T.length-1]==="`"?T.slice(1,-1):T;return $(le)},c0=j=>{try{return`<p class="katex-block">${H.renderToString(j,{...N,displayMode:!0})}</p>`}catch(h0){return N?.throwOnError&&console.log(h0),`<p class="katex-block katex-error" title="${_e(j)}">${_e(h0+"")}</p>`}},k0=(j,h0)=>c0(j[h0].content)+`
`;if(k.renderer.rules.math_inline=Q,k.renderer.rules.math_inline_block=k0,k.renderer.rules.math_inline_bare_block=k0,k.renderer.rules.math_block=k0,P){let j="math",h0=k.renderer.rules.fence;k.renderer.rules.fence=function(T,f0,le,oe,q0){let ee=T[f0];return ee.info.trim().toLowerCase()===j&&P?c0(ee.content)+`
`:h0?.call(this,T,f0,le,oe,q0)||""}}}be.default=ts});var un=import.meta.url.replace(/katex.js$/,"katex.min.css");async function xs(k){let N=await k.getRenderer("vscode.markdown-it-renderer");if(!N)throw new Error("Could not load 'vscode.markdown-it-renderer'");let H=document.createElement("link");H.rel="stylesheet",H.classList.add("markdown-style"),H.href=un;let v=document.createElement("link");v.rel="stylesheet",v.href=un,document.head.appendChild(v);let L=document.createElement("style");L.textContent=`
		.katex-error {
			color: var(--vscode-editorError-foreground);
		}
		.katex-block {
			counter-reset: katexEqnNo mmlEqnNo;
		}
	`;let R=document.createElement("template");R.classList.add("markdown-style"),R.content.appendChild(L),R.content.appendChild(H),document.head.appendChild(R);let P=cn().default,r0={};N.extendMarkdownIt(o0=>o0.use(P,{globalGroup:!0,enableBareBlocks:!0,enableFencedBlocks:!0,macros:r0}))}export{xs as activate};
